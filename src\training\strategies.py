import torch
import torch.nn as nn
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
import logging
from torch.optim.lr_scheduler import _LRScheduler
import random

logger = logging.getLogger(__name__)


class WarmupCosineAnnealingLR(_LRScheduler):
    """带预热的余弦退火学习率调度器"""
    
    def __init__(self, optimizer, warmup_epochs: int, max_epochs: int, 
                 eta_min: float = 0, last_epoch: int = -1):
        """
        初始化预热余弦退火调度器
        
        Args:
            optimizer: 优化器
            warmup_epochs: 预热轮数
            max_epochs: 最大轮数
            eta_min: 最小学习率
            last_epoch: 上一轮的编号
        """
        self.warmup_epochs = warmup_epochs
        self.max_epochs = max_epochs
        self.eta_min = eta_min
        super().__init__(optimizer, last_epoch)
        
    def get_lr(self):
        if self.last_epoch < self.warmup_epochs:
            # 预热阶段：线性增长
            return [base_lr * (self.last_epoch + 1) / self.warmup_epochs 
                   for base_lr in self.base_lrs]
        else:
            # 余弦退火阶段
            progress = (self.last_epoch - self.warmup_epochs) / (self.max_epochs - self.warmup_epochs)
            return [self.eta_min + (base_lr - self.eta_min) * 
                   (1 + np.cos(np.pi * progress)) / 2 
                   for base_lr in self.base_lrs]


class AdvancedMCGAugmentation:
    """高级MCG数据增强策略"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化增强策略
        
        Args:
            config: 增强配置
        """
        self.config = config
        self.noise_std = config.get('noise_std', 0.01)
        self.freq_range = config.get('freq_range', (20, 50))
        self.amplitude_range = config.get('amplitude_range', (0.01, 0.05))
        self.time_shift_range = config.get('time_shift_range', 0.1)
        self.channel_dropout_prob = config.get('channel_dropout_prob', 0.1)
        
    def add_physiological_noise(self, mcg_data: torch.Tensor) -> torch.Tensor:
        """
        添加生理噪声（模拟真实MCG噪声）
        
        Args:
            mcg_data: MCG数据 (batch_size, seq_len, 1, 6, 6)
            
        Returns:
            带噪声的MCG数据
        """
        batch_size, seq_len, channels, height, width = mcg_data.shape
        
        # 1. 高斯白噪声
        gaussian_noise = torch.randn_like(mcg_data) * self.noise_std
        
        # 2. 工频干扰 (50Hz)
        t = torch.linspace(0, 1, seq_len, device=mcg_data.device)
        power_line_freq = 50.0
        power_line_noise = 0.02 * torch.sin(2 * np.pi * power_line_freq * t)
        power_line_noise = power_line_noise.view(1, seq_len, 1, 1, 1).expand_as(mcg_data)
        
        # 3. 基线漂移
        baseline_drift = torch.cumsum(torch.randn(batch_size, 1, 1, height, width, device=mcg_data.device) * 0.001, dim=1)
        baseline_drift = baseline_drift.expand(-1, seq_len, channels, -1, -1)
        
        # 4. 肌电干扰 (随机高频)
        freq = torch.rand(batch_size, 1, 1, height, width, device=mcg_data.device) * 30 + 20  # 20-50Hz
        emg_noise = 0.01 * torch.sin(2 * np.pi * freq * t.view(1, seq_len, 1, 1, 1))
        
        # 合成噪声
        total_noise = gaussian_noise + power_line_noise + baseline_drift + emg_noise
        
        return mcg_data + total_noise
    
    def time_shift_augmentation(self, mcg_data: torch.Tensor) -> torch.Tensor:
        """
        时间偏移增强
        
        Args:
            mcg_data: MCG数据
            
        Returns:
            时间偏移后的数据
        """
        batch_size, seq_len, channels, height, width = mcg_data.shape
        
        augmented_data = []
        for i in range(batch_size):
            # 随机时间偏移
            shift = int(seq_len * self.time_shift_range * (2 * random.random() - 1))
            
            if shift > 0:
                # 向右偏移
                shifted = torch.cat([
                    torch.zeros(shift, channels, height, width, device=mcg_data.device),
                    mcg_data[i, :-shift]
                ], dim=0)
            elif shift < 0:
                # 向左偏移
                shifted = torch.cat([
                    mcg_data[i, -shift:],
                    torch.zeros(-shift, channels, height, width, device=mcg_data.device)
                ], dim=0)
            else:
                shifted = mcg_data[i]
            
            augmented_data.append(shifted)
        
        return torch.stack(augmented_data, dim=0)
    
    def channel_dropout(self, mcg_data: torch.Tensor) -> torch.Tensor:
        """
        通道dropout（模拟电极脱落）
        
        Args:
            mcg_data: MCG数据
            
        Returns:
            部分通道置零的数据
        """
        batch_size, seq_len, channels, height, width = mcg_data.shape
        
        for i in range(batch_size):
            if random.random() < self.channel_dropout_prob:
                # 随机选择1-2个通道位置置零
                num_dropout = random.randint(1, 2)
                for _ in range(num_dropout):
                    h_idx = random.randint(0, height - 1)
                    w_idx = random.randint(0, width - 1)
                    mcg_data[i, :, :, h_idx, w_idx] = 0
        
        return mcg_data
    
    def amplitude_scaling(self, mcg_data: torch.Tensor) -> torch.Tensor:
        """
        幅度缩放增强
        
        Args:
            mcg_data: MCG数据
            
        Returns:
            幅度缩放后的数据
        """
        batch_size = mcg_data.shape[0]
        
        for i in range(batch_size):
            # 为每个样本随机选择缩放因子
            scale_factor = random.uniform(0.8, 1.2)
            mcg_data[i] *= scale_factor
        
        return mcg_data
    
    def apply_augmentation(self, mcg_data: torch.Tensor, training: bool = True) -> torch.Tensor:
        """
        应用所有增强策略
        
        Args:
            mcg_data: 原始MCG数据
            training: 是否为训练模式
            
        Returns:
            增强后的数据
        """
        if not training:
            return mcg_data
        
        augmented = mcg_data.clone()
        
        # 随机应用不同的增强策略
        if random.random() < 0.7:  # 70%概率添加生理噪声
            augmented = self.add_physiological_noise(augmented)
        
        if random.random() < 0.3:  # 30%概率时间偏移
            augmented = self.time_shift_augmentation(augmented)
        
        if random.random() < 0.2:  # 20%概率通道dropout
            augmented = self.channel_dropout(augmented)
        
        if random.random() < 0.5:  # 50%概率幅度缩放
            augmented = self.amplitude_scaling(augmented)
        
        return augmented


class AdaptiveLossWeighting:
    """自适应损失权重调整"""
    
    def __init__(self, initial_weights: Dict[str, float], 
                 adaptation_rate: float = 0.1, min_weight: float = 0.1):
        """
        初始化自适应权重
        
        Args:
            initial_weights: 初始权重
            adaptation_rate: 适应率
            min_weight: 最小权重
        """
        self.weights = initial_weights.copy()
        self.adaptation_rate = adaptation_rate
        self.min_weight = min_weight
        self.loss_history = {key: [] for key in initial_weights.keys()}
        
    def update_weights(self, current_losses: Dict[str, float]):
        """
        根据当前损失更新权重
        
        Args:
            current_losses: 当前各任务损失
        """
        # 记录损失历史
        for key, loss in current_losses.items():
            if key in self.loss_history:
                self.loss_history[key].append(loss)
                
                # 只保留最近10个epoch的记录
                if len(self.loss_history[key]) > 10:
                    self.loss_history[key].pop(0)
        
        # 计算损失趋势并调整权重
        for key in self.weights.keys():
            if len(self.loss_history[key]) >= 2:
                # 计算损失变化趋势
                recent_losses = self.loss_history[key][-5:]  # 最近5个epoch
                trend = np.mean([recent_losses[i] - recent_losses[i-1] 
                               for i in range(1, len(recent_losses))])
                
                # 如果损失下降缓慢或上升，增加权重
                if trend >= 0:
                    self.weights[key] = min(self.weights[key] * (1 + self.adaptation_rate), 2.0)
                else:
                    # 如果损失下降快，可以适当减少权重
                    self.weights[key] = max(self.weights[key] * (1 - self.adaptation_rate/2), self.min_weight)
        
        logger.info(f"更新损失权重: {self.weights}")
    
    def get_weights(self) -> Dict[str, float]:
        """获取当前权重"""
        return self.weights.copy()


class GradientClipping:
    """梯度裁剪策略"""
    
    def __init__(self, method: str = 'norm', max_norm: float = 1.0):
        """
        初始化梯度裁剪
        
        Args:
            method: 裁剪方法 ('norm', 'value')
            max_norm: 最大范数或值
        """
        self.method = method
        self.max_norm = max_norm
        
    def clip_gradients(self, model: nn.Module) -> float:
        """
        裁剪模型梯度
        
        Args:
            model: 模型
            
        Returns:
            梯度范数
        """
        if self.method == 'norm':
            total_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), self.max_norm)
        elif self.method == 'value':
            torch.nn.utils.clip_grad_value_(model.parameters(), self.max_norm)
            # 计算总梯度范数用于监控
            total_norm = 0
            for p in model.parameters():
                if p.grad is not None:
                    param_norm = p.grad.data.norm(2)
                    total_norm += param_norm.item() ** 2
            total_norm = total_norm ** (1. / 2)
        else:
            raise ValueError(f"不支持的梯度裁剪方法: {self.method}")
        
        return float(total_norm)


class EarlyStopping:
    """改进的早停策略"""
    
    def __init__(self, patience: int = 10, min_delta: float = 1e-4, 
                 mode: str = 'min', restore_best: bool = True):
        """
        初始化早停
        
        Args:
            patience: 容忍轮数
            min_delta: 最小改善
            mode: 监控模式
            restore_best: 是否恢复最佳权重
        """
        self.patience = patience
        self.min_delta = min_delta
        self.mode = mode
        self.restore_best = restore_best
        
        self.best_score = None
        self.counter = 0
        self.best_weights = None
        self.early_stop = False
        
        if mode == 'min':
            self.is_better = lambda current, best: current < best - min_delta
        else:
            self.is_better = lambda current, best: current > best + min_delta
    
    def __call__(self, score: float, model: nn.Module) -> bool:
        """
        检查是否应该早停
        
        Args:
            score: 当前分数
            model: 模型
            
        Returns:
            是否早停
        """
        if self.best_score is None:
            self.best_score = score
            self.save_checkpoint(model)
        elif self.is_better(score, self.best_score):
            self.best_score = score
            self.counter = 0
            self.save_checkpoint(model)
        else:
            self.counter += 1
            
        if self.counter >= self.patience:
            self.early_stop = True
            if self.restore_best and self.best_weights is not None:
                model.load_state_dict(self.best_weights)
                logger.info("恢复最佳模型权重")
        
        return self.early_stop
    
    def save_checkpoint(self, model: nn.Module):
        """保存检查点"""
        if self.restore_best:
            self.best_weights = model.state_dict().copy()


class TrainingStrategy:
    """训练策略管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化训练策略
        
        Args:
            config: 训练配置
        """
        self.config = config
        
        # 初始化各个组件
        self.augmentation = None
        self.adaptive_weights = None
        self.gradient_clipping = None
        self.early_stopping = None
        
        self._setup_components()
    
    def _setup_components(self):
        """设置训练组件"""
        # 数据增强
        if self.config.get('use_advanced_augmentation', False):
            aug_config = self.config.get('augmentation', {})
            self.augmentation = AdvancedMCGAugmentation(aug_config)
        
        # 自适应权重
        if self.config.get('use_adaptive_weights', False):
            initial_weights = {
                'risk_weight': self.config.get('loss_weights', {}).get('risk_score_weight', 1.0),
                'conclusion_weight': self.config.get('loss_weights', {}).get('conclusion_weight', 1.0)
            }
            self.adaptive_weights = AdaptiveLossWeighting(initial_weights)
        
        # 梯度裁剪
        if self.config.get('gradient_clipping', {}).get('enabled', False):
            clip_config = self.config['gradient_clipping']
            self.gradient_clipping = GradientClipping(
                method=clip_config.get('method', 'norm'),
                max_norm=clip_config.get('max_norm', 1.0)
            )
        
        # 早停
        if self.config.get('early_stopping', {}).get('patience', 0) > 0:
            es_config = self.config['early_stopping']
            self.early_stopping = EarlyStopping(
                patience=es_config['patience'],
                min_delta=es_config.get('min_delta', 1e-4),
                mode='min',
                restore_best=True
            )
    
    def create_optimizer(self, model: nn.Module) -> torch.optim.Optimizer:
        """
        创建优化器
        
        Args:
            model: 模型
            
        Returns:
            优化器
        """
        optimizer_config = self.config.get('optimizer', {})
        optimizer_type = optimizer_config.get('type', 'AdamW')
        
        # 不同层的学习率
        if self.config.get('use_layer_wise_lr', False):
            param_groups = self._create_param_groups(model)
        else:
            param_groups = model.parameters()
        
        if optimizer_type == 'AdamW':
            optimizer = torch.optim.AdamW(
                param_groups,
                lr=self.config.get('learning_rate', 1e-3),
                weight_decay=self.config.get('weight_decay', 1e-4),
                betas=optimizer_config.get('betas', (0.9, 0.999))
            )
        elif optimizer_type == 'Adam':
            optimizer = torch.optim.Adam(
                param_groups,
                lr=self.config.get('learning_rate', 1e-3),
                weight_decay=self.config.get('weight_decay', 1e-4),
                betas=optimizer_config.get('betas', (0.9, 0.999))
            )
        elif optimizer_type == 'SGD':
            optimizer = torch.optim.SGD(
                param_groups,
                lr=self.config.get('learning_rate', 1e-3),
                weight_decay=self.config.get('weight_decay', 1e-4),
                momentum=optimizer_config.get('momentum', 0.9)
            )
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_type}")
        
        return optimizer
    
    def create_scheduler(self, optimizer: torch.optim.Optimizer) -> torch.optim.lr_scheduler._LRScheduler:
        """
        创建学习率调度器
        
        Args:
            optimizer: 优化器
            
        Returns:
            学习率调度器
        """
        scheduler_config = self.config.get('scheduler', {})
        scheduler_type = scheduler_config.get('type', 'cosine')
        
        if scheduler_type == 'warmup_cosine':
            scheduler = WarmupCosineAnnealingLR(
                optimizer,
                warmup_epochs=scheduler_config.get('warmup_epochs', 10),
                max_epochs=self.config.get('num_epochs', 100),
                eta_min=scheduler_config.get('min_lr', 1e-6)
            )
        elif scheduler_type == 'cosine':
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer,
                T_max=self.config.get('num_epochs', 100),
                eta_min=scheduler_config.get('min_lr', 1e-6)
            )
        elif scheduler_type == 'step':
            scheduler = torch.optim.lr_scheduler.StepLR(
                optimizer,
                step_size=scheduler_config.get('step_size', 30),
                gamma=scheduler_config.get('gamma', 0.1)
            )
        elif scheduler_type == 'plateau':
            scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                optimizer,
                mode='min',
                factor=scheduler_config.get('factor', 0.5),
                patience=scheduler_config.get('patience', 10),
                min_lr=scheduler_config.get('min_lr', 1e-6)
            )
        else:
            raise ValueError(f"不支持的调度器类型: {scheduler_type}")
        
        return scheduler
    
    def _create_param_groups(self, model: nn.Module) -> List[Dict]:
        """创建不同层的参数组"""
        # 简化的分层学习率设置
        param_groups = []
        
        # 空间提取器使用较小学习率
        if hasattr(model, 'spatial_extractor'):
            param_groups.append({
                'params': model.spatial_extractor.parameters(),
                'lr': self.config.get('learning_rate', 1e-3) * 0.5
            })
        
        # 时序模型使用标准学习率
        if hasattr(model, 'temporal_model'):
            param_groups.append({
                'params': model.temporal_model.parameters(),
                'lr': self.config.get('learning_rate', 1e-3)
            })
        
        # 预测头使用较大学习率
        if hasattr(model, 'prediction_heads'):
            param_groups.append({
                'params': model.prediction_heads.parameters(),
                'lr': self.config.get('learning_rate', 1e-3) * 2.0
            })
        
        # 其他参数使用标准学习率
        other_params = []
        named_params = dict(model.named_parameters())
        for name, param in named_params.items():
            if not any(comp in name for comp in ['spatial_extractor', 'temporal_model', 'prediction_heads']):
                other_params.append(param)
        
        if other_params:
            param_groups.append({
                'params': other_params,
                'lr': self.config.get('learning_rate', 1e-3)
            })
        
        return param_groups
    
    def apply_augmentation(self, mcg_data: torch.Tensor, training: bool = True) -> torch.Tensor:
        """应用数据增强"""
        if self.augmentation is not None:
            return self.augmentation.apply_augmentation(mcg_data, training)
        return mcg_data
    
    def update_loss_weights(self, losses: Dict[str, float]) -> Dict[str, float]:
        """更新损失权重"""
        if self.adaptive_weights is not None:
            self.adaptive_weights.update_weights(losses)
            return self.adaptive_weights.get_weights()
        
        # 返回默认权重
        return {
            'risk_weight': self.config.get('loss_weights', {}).get('risk_score_weight', 1.0),
            'conclusion_weight': self.config.get('loss_weights', {}).get('conclusion_weight', 1.0)
        }
    
    def clip_gradients(self, model: nn.Module) -> Optional[float]:
        """裁剪梯度"""
        if self.gradient_clipping is not None:
            return self.gradient_clipping.clip_gradients(model)
        return None
    
    def check_early_stopping(self, score: float, model: nn.Module) -> bool:
        """检查早停"""
        if self.early_stopping is not None:
            return self.early_stopping(score, model)
        return False