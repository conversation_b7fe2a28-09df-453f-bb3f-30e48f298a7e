"""
模型加载器 - 支持从配置文件创建42通道多模态模型
"""

import os
import yaml
import logging
from typing import Dict, Any
from .mcg_model import create_mcg_model, get_available_models, get_default_config
from .spatial_cnn_gru import create_spatial_aware_model

logger = logging.getLogger(__name__)

def load_model_config(config_path: str) -> Dict[str, Any]:
    """加载模型配置文件"""
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"模型配置文件不存在: {config_path}")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        model_config = yaml.safe_load(f)
    
    logger.info(f"已加载模型配置: {config_path}")
    return model_config

def create_model_from_path(model_config_path: str, clinical_feature_dim: int = 0):
    """从配置文件路径创建42通道多模态模型"""
    
    # 加载模型配置
    model_config = load_model_config(model_config_path)
    
    # 获取架构类型
    architecture = model_config.get('architecture')
    if not architecture:
        raise ValueError(f"模型配置文件中缺少 'architecture' 字段: {model_config_path}")
    
    logger.info(f"创建模型: {architecture}")
    
    # 检查是否为空间感知模型
    if architecture in ['spatial_cnn_gru', 'spatial_unet', 'spatial_vit']:
        logger.info(f"创建空间感知模型: {architecture}")
        model = create_spatial_aware_model(model_config, clinical_feature_dim)
    else:
        # 检查是否为支持的42通道模型
        available_models = get_available_models()
        if architecture not in available_models:
            raise ValueError(f"不支持的模型架构: {architecture}, 可用架构: {list(available_models.keys())}")
        
        logger.info(f"创建42通道多模态模型: {architecture}")
        
        # 使用文件中的完整配置创建模型，确保架构正确
        # 移除'architecture'字段，因为create_mcg_model用配置字典而非architecture参数
        config_for_model = model_config.copy()
        
        # 创建模型
        model = create_mcg_model(config_for_model, clinical_feature_dim)
    
    return model

def create_model_from_config(main_config, clinical_feature_dim: int = 0):
    """从主配置创建模型 (支持model_config_path或直接配置)"""
    
    # 如果指定了model_config_path，优先使用
    if hasattr(main_config, 'model_config_path') and main_config.model_config_path:
        logger.info(f"使用模型配置文件: {main_config.model_config_path}")
        return create_model_from_path(main_config.model_config_path, clinical_feature_dim)
    
    # 否则使用主配置中的模型配置
    logger.info("使用主配置中的模型设置")
    architecture = getattr(main_config, 'architecture', 'multimodal_unet')
    
    # 检查是否为42通道模型
    available_models = get_available_models()
    if architecture not in available_models:
        logger.warning(f"架构 {architecture} 不在42通道模型中，使用默认UNet模型")
        architecture = 'multimodal_unet'
    
    # 构建模型配置字典
    model_config = get_default_config(architecture)
    
    # 从主配置中更新对应的配置项
    config_mapping = {
        'signal_cnn': 'signal_cnn',
        'signal_unet': 'signal_unet', 
        'signal_vit': 'signal_vit',
        'timepoint': 'timepoint',
        'timepoint_unet': 'timepoint_unet',
        'timepoint_vit': 'timepoint_vit',
        'gru': 'gru',
        'fusion': 'fusion',
        'clinical': 'clinical',
        'final_fusion': 'final_fusion',
        'output': 'output'
    }
    
    for config_key, attr_name in config_mapping.items():
        if hasattr(main_config, attr_name):
            attr_value = getattr(main_config, attr_name)
            if attr_value:  # 只有非空字典才更新
                model_config[config_key] = attr_value
    
    # 创建模型
    model = create_mcg_model(model_config, clinical_feature_dim)
    
    return model