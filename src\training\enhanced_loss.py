"""
增强版多任务损失函数 - 支持风险分类选项
Enhanced Multi-Task Loss Function with Risk Classification Options
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Any, Optional
import logging

logger = logging.getLogger(__name__)


class FlexibleRiskLoss(nn.Module):
    """灵活的风险损失函数，支持回归、分类、混合模式"""
    
    def __init__(self, mode: str = "regression", threshold: float = 50.0,
                 loss_type: str = "bce", use_focal: bool = False,
                 focal_alpha: float = 0.25, focal_gamma: float = 2.0):
        """
        初始化灵活风险损失函数
        
        Args:
            mode: 风险处理模式 ("regression", "classification", "both")
            threshold: 分类模式的阈值 (默认50%)
            loss_type: 损失函数类型 ("bce", "mse", "focal", "ce")
            use_focal: 是否使用focal loss (仅分类模式)
            focal_alpha: focal loss的alpha参数
            focal_gamma: focal loss的gamma参数
        """
        super().__init__()
        
        self.mode = mode
        self.threshold = threshold / 100.0  # 转换为0-1范围
        self.loss_type = loss_type
        self.use_focal = use_focal
        
        # 根据模式和类型选择损失函数
        if mode == "regression":
            if loss_type == "bce":
                self.loss_fn = nn.BCELoss()
            elif loss_type == "mse":
                self.loss_fn = nn.MSELoss()
            elif loss_type == "smooth_l1":
                self.loss_fn = nn.SmoothL1Loss()
            else:
                raise ValueError(f"不支持的回归损失类型: {loss_type}")
                
        elif mode == "classification":
            if use_focal:
                from .loss import FocalLoss
                self.loss_fn = FocalLoss(alpha=focal_alpha, gamma=focal_gamma)
            elif loss_type == "bce":
                self.loss_fn = nn.BCEWithLogitsLoss()
            elif loss_type == "ce":
                self.loss_fn = nn.CrossEntropyLoss()
            else:
                raise ValueError(f"不支持的分类损失类型: {loss_type}")
                
        elif mode == "both":
            # 混合模式使用两个损失函数
            self.regression_loss = nn.BCELoss()
            if use_focal:
                from .loss import FocalLoss
                self.classification_loss = FocalLoss(alpha=focal_alpha, gamma=focal_gamma)
            else:
                self.classification_loss = nn.BCEWithLogitsLoss()
        else:
            raise ValueError(f"不支持的模式: {mode}")
        
        logger.info(f"FlexibleRiskLoss初始化: mode={mode}, threshold={threshold}%, "
                   f"loss_type={loss_type}, use_focal={use_focal}")
    
    def convert_to_classification_targets(self, stenosis_values: torch.Tensor) -> torch.Tensor:
        """
        将原始狭窄程度转换为分类标签
        
        Args:
            stenosis_values: 原始狭窄程度 (0-100)
            
        Returns:
            分类标签 (0 或 1)
        """
        # 假设输入是0-100范围，需要转换为0-1范围进行比较
        normalized_values = stenosis_values / 100.0 if stenosis_values.max() > 1.0 else stenosis_values
        return (normalized_values > self.threshold).float()
    
    def convert_to_regression_targets(self, stenosis_values: torch.Tensor) -> torch.Tensor:
        """
        将原始狭窄程度转换为风险评分 (sigmoid变换)
        
        Args:
            stenosis_values: 原始狭窄程度 (0-100)
            
        Returns:
            风险评分 (0-1)
        """
        # sigmoid(10 * stenosis/100 - 5)
        if stenosis_values.max() > 1.0:
            normalized_values = stenosis_values / 100.0
        else:
            normalized_values = stenosis_values
        return torch.sigmoid(10 * normalized_values - 5)
    
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor,
                original_stenosis: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            predictions: 模型预测值
            targets: 目标值 (可能是风险评分或分类标签)
            original_stenosis: 原始狭窄程度 (用于模式转换)
            
        Returns:
            包含损失组件的字典
        """
        loss_dict = {}
        
        # 处理NaN值
        valid_mask = ~torch.isnan(targets)
        if not valid_mask.any():
            return {"risk_loss": torch.tensor(0.0, device=predictions.device)}
        
        valid_pred = predictions[valid_mask]
        valid_targets = targets[valid_mask]
        
        if self.mode == "regression":
            # 如果提供了原始狭窄程度，转换为回归目标
            if original_stenosis is not None:
                valid_original = original_stenosis[valid_mask]
                valid_targets = self.convert_to_regression_targets(valid_original)
            
            risk_loss = self.loss_fn(valid_pred, valid_targets)
            loss_dict["risk_loss"] = risk_loss
            
        elif self.mode == "classification":
            # 如果提供了原始狭窄程度，转换为分类目标
            if original_stenosis is not None:
                valid_original = original_stenosis[valid_mask]
                valid_targets = self.convert_to_classification_targets(valid_original)
            
            # 分类模式下，predictions应该是logits
            if self.use_focal or self.loss_type == "bce":
                risk_loss = self.loss_fn(valid_pred, valid_targets)
            else:  # CrossEntropyLoss
                # 需要转换标签格式
                valid_targets = valid_targets.long()
                risk_loss = self.loss_fn(valid_pred, valid_targets)
            
            loss_dict["risk_loss"] = risk_loss
            
        elif self.mode == "both":
            # 混合模式：同时计算回归和分类损失
            if original_stenosis is not None:
                valid_original = original_stenosis[valid_mask]
                regression_targets = self.convert_to_regression_targets(valid_original)
                classification_targets = self.convert_to_classification_targets(valid_original)
            else:
                # 如果没有原始数据，假设targets是回归目标
                regression_targets = valid_targets
                classification_targets = (valid_targets > self.threshold).float()
            
            # 假设predictions包含回归和分类两部分输出
            if valid_pred.shape[-1] == 2:
                reg_pred = valid_pred[:, 0]
                cls_pred = valid_pred[:, 1]
            else:
                # 只有一个输出，同时用于回归和分类
                reg_pred = torch.sigmoid(valid_pred)  # 用于回归
                cls_pred = valid_pred  # 用于分类 (logits)
            
            regression_loss = self.regression_loss(reg_pred, regression_targets)
            classification_loss = self.classification_loss(cls_pred, classification_targets)
            
            loss_dict["risk_regression_loss"] = regression_loss
            loss_dict["risk_classification_loss"] = classification_loss
            loss_dict["risk_loss"] = regression_loss + classification_loss
        
        return loss_dict


class EnhancedMultiTaskLoss(nn.Module):
    """增强版多任务损失函数"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        从配置初始化增强多任务损失函数
        
        Args:
            config: 损失配置字典
        """
        super().__init__()
        
        loss_weights = config.get("loss_weights", {})
        
        # 风险损失配置
        self.risk_mode = loss_weights.get("risk_mode", "regression")
        risk_threshold = loss_weights.get("risk_threshold", 50.0)
        risk_loss_type = loss_weights.get("risk_loss_type", "bce")
        use_focal_risk = loss_weights.get("use_focal_risk", False)
        
        # 创建风险损失函数
        self.risk_loss_fn = FlexibleRiskLoss(
            mode=self.risk_mode,
            threshold=risk_threshold,
            loss_type=risk_loss_type,
            use_focal=use_focal_risk,
            focal_alpha=loss_weights.get("focal_loss_alpha", 0.25),
            focal_gamma=loss_weights.get("focal_loss_gamma", 2.0)
        )
        
        # 结论损失函数
        conclusion_loss_type = loss_weights.get("conclusion_loss_type", "focal")
        if conclusion_loss_type == "focal":
            from .loss import FocalLoss
            self.conclusion_loss_fn = FocalLoss(
                alpha=loss_weights.get("focal_loss_alpha", 0.25),
                gamma=loss_weights.get("focal_loss_gamma", 2.0)
            )
        else:
            self.conclusion_loss_fn = nn.BCEWithLogitsLoss()
        
        # 权重设置
        if self.risk_mode == "regression":
            self.risk_weight = loss_weights.get("risk_regression_weight", 1.0)
        elif self.risk_mode == "classification":
            self.risk_weight = loss_weights.get("risk_classification_weight", 1.0)
        else:  # both
            self.risk_regression_weight = loss_weights.get("risk_both_regression_weight", 0.5)
            self.risk_classification_weight = loss_weights.get("risk_both_classification_weight", 0.5)
            self.risk_weight = 1.0  # 总风险权重
        
        self.conclusion_weight = loss_weights.get("conclusion_weight", 1.0)
        
        logger.info(f"EnhancedMultiTaskLoss初始化: risk_mode={self.risk_mode}, "
                   f"risk_weight={self.risk_weight}, conclusion_weight={self.conclusion_weight}")
    
    def forward(self, predictions: Dict[str, torch.Tensor], 
                targets: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            predictions: 包含模型预测的字典
                - 'risk_scores': 风险评分预测
                - 'conclusion_logits': 结论logits预测
            targets: 包含真实标签的字典
                - 'risk_scores': 风险评分目标
                - 'conclusion': 结论目标
                - 'original_stenosis': 原始狭窄程度 (可选)
                
        Returns:
            包含损失组件的字典
        """
        # 风险损失计算
        risk_pred = predictions['risk_scores']
        risk_targets = targets['risk_scores']
        original_stenosis = targets.get('original_stenosis', None)
        
        risk_loss_dict = self.risk_loss_fn(
            risk_pred, risk_targets, original_stenosis
        )
        
        # 结论损失计算
        conclusion_logits = predictions['conclusion_logits']
        conclusion_targets = targets['conclusion']
        
        valid_conclusion_mask = ~torch.isnan(conclusion_targets)
        if valid_conclusion_mask.any():
            conclusion_loss = self.conclusion_loss_fn(
                conclusion_logits[valid_conclusion_mask],
                conclusion_targets[valid_conclusion_mask]
            )
        else:
            conclusion_loss = torch.tensor(0.0, device=conclusion_logits.device)
        
        # 组合总损失
        if self.risk_mode == "both":
            # 混合模式下分别计算权重
            total_risk_loss = (
                self.risk_regression_weight * risk_loss_dict.get("risk_regression_loss", 0) +
                self.risk_classification_weight * risk_loss_dict.get("risk_classification_loss", 0)
            )
        else:
            total_risk_loss = risk_loss_dict.get("risk_loss", 0)
        
        total_loss = (
            self.risk_weight * total_risk_loss +
            self.conclusion_weight * conclusion_loss
        )
        
        # 构建返回字典
        result = {
            "total_loss": total_loss,
            "conclusion_loss": conclusion_loss,
            "risk_weight": self.risk_weight,
            "conclusion_weight": self.conclusion_weight
        }
        
        # 添加风险损失组件
        result.update(risk_loss_dict)
        
        return result


def create_enhanced_loss_function(config: Dict[str, Any]) -> nn.Module:
    """
    创建增强版损失函数
    
    Args:
        config: 训练配置字典
        
    Returns:
        损失函数模块
    """
    loss_type = config.get('loss_weights', {}).get('loss_type', 'enhanced_multitask')
    
    if loss_type == 'enhanced_multitask':
        return EnhancedMultiTaskLoss(config)
    else:
        # 回退到原始实现
        from .loss import create_loss_function
        return create_loss_function(config)