"""
终极42通道多模态UNet模型
整合所有历史优化策略：分离式处理 + 防过拟合 + 跨模态融合 + UNet架构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)


class UNetBlock(nn.Module):
    """UNet卷积块 - 整合防过拟合策略"""
    
    def __init__(self, in_channels: int, out_channels: int, dropout: float = 0.2, use_residual: bool = True):
        super().__init__()
        
        self.use_residual = use_residual and (in_channels == out_channels)
        
        # 双卷积块
        self.conv1 = nn.Conv1d(in_channels, out_channels, kernel_size=3, padding=1, bias=False)
        self.bn1 = nn.BatchNorm1d(out_channels)
        
        self.conv2 = nn.Conv1d(out_channels, out_channels, kernel_size=3, padding=1, bias=False)
        self.bn2 = nn.BatchNorm1d(out_channels)
        
        self.dropout = nn.Dropout1d(dropout)
        self.activation = nn.GELU()
        
        # 残差连接
        if self.use_residual and in_channels != out_channels:
            self.residual_conv = nn.Conv1d(in_channels, out_channels, 1, bias=False)
            self.residual_bn = nn.BatchNorm1d(out_channels)
    
    def forward(self, x):
        identity = x
        
        # 第一个卷积
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.activation(out)
        out = self.dropout(out)
        
        # 第二个卷积
        out = self.conv2(out)
        out = self.bn2(out)
        
        # 残差连接
        if self.use_residual:
            if hasattr(self, 'residual_conv'):
                identity = self.residual_conv(identity)
                identity = self.residual_bn(identity)
            out = out + identity
        
        return self.activation(out)


class SignalUNetEncoder(nn.Module):
    """信号UNet编码器 - 专门处理36个信号通道"""
    
    def __init__(self, input_channels: int = 36, base_channels: int = 64, 
                 num_blocks: int = 4, dropout: float = 0.2):
        super().__init__()
        
        self.blocks = nn.ModuleList()
        self.pools = nn.ModuleList()
        
        channels = [input_channels]
        for i in range(num_blocks):
            out_ch = base_channels * (2 ** i)  # 64, 128, 256, 512
            channels.append(out_ch)
        
        for i in range(num_blocks):
            in_ch = channels[i]
            out_ch = channels[i + 1]
            
            # UNet块
            block = UNetBlock(in_ch, out_ch, dropout, use_residual=True)
            self.blocks.append(block)
            
            # 下采样（除了最后一层）
            if i < num_blocks - 1:
                self.pools.append(nn.MaxPool1d(2))
        
        self.output_channels = channels
        logger.info(f"SignalUNetEncoder: {input_channels} → {channels[-1]}, blocks={num_blocks}")
    
    def forward(self, x):
        features = []
        
        for i, block in enumerate(self.blocks):
            x = block(x)
            features.append(x)
            
            if i < len(self.pools):
                x = self.pools[i](x)
        
        return features


class SignalUNetDecoder(nn.Module):
    """信号UNet解码器"""
    
    def __init__(self, encoder_channels: List[int], dropout: float = 0.2):
        super().__init__()
        
        self.upsamples = nn.ModuleList()
        self.blocks = nn.ModuleList()
        self.encoder_channels = encoder_channels
        
        # encoder_channels = [36, 64, 128, 256, 512]
        # 解码路径只处理编码器产生的特征层 [64, 128, 256, 512]
        # 跳跃连接对应: [256, 128, 64] (不包括输入层36和最深层512)
        
        decoder_pairs = []
        for i in range(len(encoder_channels) - 1, 1, -1):  # 从512到128
            current_ch = encoder_channels[i]      # 当前层 (512, 256, 128)
            target_ch = encoder_channels[i - 1]   # 目标层 (256, 128, 64)
            decoder_pairs.append((current_ch, target_ch, True))  # 有跳跃连接
        
        # 最后一层：从64到36，没有跳跃连接（因为输入是原始数据）
        if len(encoder_channels) >= 2:
            decoder_pairs.append((encoder_channels[1], encoder_channels[0], False))
        
        for current_ch, target_ch, has_skip in decoder_pairs:
            # 上采样：将当前通道数减半
            upsampled_ch = current_ch // 2
            self.upsamples.append(
                nn.ConvTranspose1d(current_ch, upsampled_ch, kernel_size=2, stride=2)
            )
            
            # 计算解码块的输入通道数
            if has_skip:
                # 有跳跃连接：上采样通道 + 跳跃连接通道
                decoder_input_ch = upsampled_ch + target_ch
            else:
                # 无跳跃连接：只有上采样通道
                decoder_input_ch = upsampled_ch
            
            # 解码块
            block = UNetBlock(decoder_input_ch, target_ch, dropout, use_residual=False)
            self.blocks.append(block)
        
        logger.info(f"SignalUNetDecoder: {len(decoder_pairs)} decoder steps, channels={encoder_channels}")
    
    def forward(self, encoder_features):
        x = encoder_features[-1]  # 最深的特征 (batch, 512, seq_len)
        
        # encoder_features实际是: [64-ch, 128-ch, 256-ch, 512-ch] (不包括原始输入36-ch)
        # 跳跃连接: 解码时需要 [256-ch, 128-ch, 64-ch] (倒序，不包括最深层512-ch)
        skip_features = encoder_features[:-1]  # [64, 128, 256] - 除了最深层512
        skip_features.reverse()  # [256, 128, 64] - 反转为解码顺序
        
        for i, (upsample, block) in enumerate(zip(self.upsamples, self.blocks)):
            # 上采样
            x = upsample(x)
            
            # 跳跃连接（前面几层有，最后一层没有）
            if i < len(skip_features):
                skip = skip_features[i]
                
                # 处理尺寸不匹配
                if x.size(2) != skip.size(2):
                    diff = skip.size(2) - x.size(2)
                    if diff > 0:
                        x = F.pad(x, (diff // 2, diff - diff // 2))
                    else:
                        # 如果x比skip大，则裁剪x
                        diff = abs(diff)
                        x = x[:, :, diff//2:x.size(2)-diff//2] if diff//2 < x.size(2) else x
                
                # 拼接跳跃连接
                x = torch.cat([skip, x], dim=1)
            
            # 解码卷积
            x = block(x)
        
        return x


class TimepointUNetProcessor(nn.Module):
    """时刻点UNet处理器 - 轻量级UNet结构"""
    
    def __init__(self, timepoint_channels: int = 6, hidden_dim: int = 128, 
                 num_layers: int = 3, dropout: float = 0.2):
        super().__init__()
        
        # 编码器
        self.encoder_blocks = nn.ModuleList()
        self.encoder_pools = nn.ModuleList()
        
        in_ch = timepoint_channels
        for i in range(num_layers):
            out_ch = hidden_dim
            
            block = UNetBlock(in_ch, out_ch, dropout)
            self.encoder_blocks.append(block)
            
            if i < num_layers - 1:
                self.encoder_pools.append(nn.MaxPool1d(2))
            
            in_ch = out_ch
        
        # 全局池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # 特征增强
        self.enhancement = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim)
        )
        
        self.output_dim = hidden_dim
        logger.info(f"TimepointUNetProcessor: {timepoint_channels} → {hidden_dim}, layers={num_layers}")
    
    def forward(self, x):
        # 编码
        for i, block in enumerate(self.encoder_blocks):
            x = block(x)
            if i < len(self.encoder_pools):
                x = self.encoder_pools[i](x)
        
        # 全局池化
        x = self.global_pool(x).squeeze(-1)  # (batch_size, hidden_dim)
        
        # 特征增强
        enhanced = self.enhancement(x)
        return enhanced


class MultiScaleFusion(nn.Module):
    """多尺度特征融合 - UNet特有的特征融合策略"""
    
    def __init__(self, signal_dim: int, timepoint_dim: int, dropout: float = 0.1):
        super().__init__()
        
        # 多尺度投影
        self.signal_projections = nn.ModuleList([
            nn.Linear(signal_dim, signal_dim // 2),
            nn.Linear(signal_dim, signal_dim),
            nn.Linear(signal_dim, signal_dim * 2)
        ])
        
        self.timepoint_projections = nn.ModuleList([
            nn.Linear(timepoint_dim, timepoint_dim // 2),
            nn.Linear(timepoint_dim, timepoint_dim),
            nn.Linear(timepoint_dim, timepoint_dim * 2)
        ])
        
        # 注意力权重
        total_dim = (signal_dim // 2 + signal_dim + signal_dim * 2 + 
                     timepoint_dim // 2 + timepoint_dim + timepoint_dim * 2)
        
        self.attention = nn.Sequential(
            nn.Linear(total_dim, total_dim // 4),
            nn.GELU(),
            nn.Linear(total_dim // 4, 6),  # 6个尺度的权重
            nn.Softmax(dim=1)
        )
        
        # 输出投影
        self.output_proj = nn.Sequential(
            nn.Linear(total_dim, signal_dim + timepoint_dim),
            nn.LayerNorm(signal_dim + timepoint_dim),
            nn.GELU(),
            nn.Dropout(dropout)
        )
        
        self.output_dim = signal_dim + timepoint_dim
        logger.info(f"MultiScaleFusion: signal={signal_dim}D + timepoint={timepoint_dim}D → {self.output_dim}D")
    
    def forward(self, signal_features, timepoint_features):
        # 多尺度投影
        signal_scales = [proj(signal_features) for proj in self.signal_projections]
        timepoint_scales = [proj(timepoint_features) for proj in self.timepoint_projections]
        
        # 拼接所有尺度
        all_scales = signal_scales + timepoint_scales
        concat_features = torch.cat(all_scales, dim=1)
        
        # 注意力权重
        attention_weights = self.attention(concat_features)  # (batch, 6)
        
        # 加权融合
        weighted_scales = []
        for i, scale_features in enumerate(all_scales):
            weight = attention_weights[:, i:i+1]  # (batch, 1)
            weighted_scales.append(weight * scale_features)
        
        weighted_concat = torch.cat(weighted_scales, dim=1)
        
        # 输出投影
        fused_features = self.output_proj(weighted_concat)
        return fused_features


class MultiModal42ChannelUNet(nn.Module):
    """终极42通道多模态UNet模型"""
    
    def __init__(self, config: Dict[str, Any], clinical_feature_dim: int = 0):
        super().__init__()
        
        self.config = config
        self.clinical_feature_dim = clinical_feature_dim
        
        # 信号UNet配置
        signal_config = config.get('signal_unet', {})
        base_channels = signal_config.get('base_channels', 64)
        num_blocks = signal_config.get('num_blocks', 4)
        dropout = signal_config.get('dropout', 0.2)
        
        # 信号UNet编码器和解码器
        self.signal_encoder = SignalUNetEncoder(
            input_channels=36,
            base_channels=base_channels,
            num_blocks=num_blocks,
            dropout=dropout
        )
        
        self.signal_decoder = SignalUNetDecoder(
            encoder_channels=self.signal_encoder.output_channels,
            dropout=dropout
        )
        
        # 时刻点UNet处理器
        timepoint_config = config.get('timepoint_unet', {})
        self.timepoint_processor = TimepointUNetProcessor(
            timepoint_channels=6,
            hidden_dim=timepoint_config.get('hidden_dim', 128),
            num_layers=timepoint_config.get('num_layers', 3),
            dropout=timepoint_config.get('dropout', 0.2)
        )
        
        # 信号特征全局池化
        self.global_avg_pool = nn.AdaptiveAvgPool1d(1)
        self.global_max_pool = nn.AdaptiveMaxPool1d(1)
        
        # 计算信号特征维度
        final_signal_channels = self.signal_encoder.output_channels[0]  # 最小的编码器通道数
        signal_feature_dim = final_signal_channels * 2  # avg + max pooling
        
        # 多尺度融合
        fusion_config = config.get('fusion', {})
        self.multi_scale_fusion = MultiScaleFusion(
            signal_dim=signal_feature_dim,
            timepoint_dim=self.timepoint_processor.output_dim,
            dropout=fusion_config.get('dropout', 0.1)
        )
        
        # 临床特征处理
        clinical_hidden = config.get('clinical', {}).get('hidden_dim', 128)
        if clinical_feature_dim > 0:
            self.clinical_processor = nn.Sequential(
                nn.Linear(clinical_feature_dim, clinical_hidden),
                nn.LayerNorm(clinical_hidden),
                nn.GELU(),
                nn.Dropout(config.get('clinical', {}).get('dropout', 0.3)),
                nn.Linear(clinical_hidden, clinical_hidden),
                nn.LayerNorm(clinical_hidden)
            )
        
        # 最终特征融合
        fusion_input_dim = (self.multi_scale_fusion.output_dim + 
                           (clinical_hidden if clinical_feature_dim > 0 else 0))
        
        final_hidden = config.get('final_fusion', {}).get('hidden_dim', 512)
        self.final_fusion = nn.Sequential(
            nn.Linear(fusion_input_dim, final_hidden),
            nn.LayerNorm(final_hidden),
            nn.GELU(),
            nn.Dropout(config.get('final_fusion', {}).get('dropout', 0.3)),
            nn.Linear(final_hidden, final_hidden),
            nn.LayerNorm(final_hidden),
            nn.GELU()
        )
        
        # 多任务预测头
        output_config = config.get('output', {})
        
        # 风险评分头
        risk_hidden = output_config.get('risk_head_hidden', 256)
        self.risk_head = nn.Sequential(
            nn.Linear(final_hidden, risk_hidden),
            nn.LayerNorm(risk_hidden),
            nn.GELU(),
            nn.Dropout(output_config.get('dropout', 0.4)),
            nn.Linear(risk_hidden, risk_hidden // 2),
            nn.LayerNorm(risk_hidden // 2),
            nn.GELU(),
            nn.Dropout(output_config.get('dropout', 0.4)),
            nn.Linear(risk_hidden // 2, 4),
            nn.Sigmoid()
        )
        
        # 结论预测头
        conclusion_hidden = output_config.get('conclusion_head_hidden', 128)
        self.conclusion_head = nn.Sequential(
            nn.Linear(final_hidden, conclusion_hidden),
            nn.LayerNorm(conclusion_hidden),
            nn.GELU(),
            nn.Dropout(output_config.get('dropout', 0.4)),
            nn.Linear(conclusion_hidden, conclusion_hidden // 2),
            nn.LayerNorm(conclusion_hidden // 2),
            nn.GELU(),
            nn.Dropout(output_config.get('dropout', 0.4)),
            nn.Linear(conclusion_hidden // 2, 1)
        )
        
        # 权重初始化
        self.apply(self._init_weights)
        
        # 模型信息
        total_params = sum(p.numel() for p in self.parameters())
        logger.info(f"MultiModal42ChannelUNet initialized: {total_params:,} parameters")
    
    def _init_weights(self, module):
        """权重初始化"""
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight, gain=1.0)
            if module.bias is not None:
                nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Conv1d):
            nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
        elif isinstance(module, (nn.BatchNorm1d, nn.LayerNorm)):
            nn.init.ones_(module.weight)
            nn.init.zeros_(module.bias)
    
    def forward(self, multimodal_data: torch.Tensor, clinical_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        # 转换格式
        multimodal_data = multimodal_data.transpose(1, 2)  # (batch, 42, seq_len)
        
        # 分离通道
        signal_data = multimodal_data[:, :36, :]  # 前36通道
        timepoint_data = multimodal_data[:, 36:, :]  # 后6通道
        
        # 信号UNet处理
        encoder_features = self.signal_encoder(signal_data)
        decoded_features = self.signal_decoder(encoder_features)
        
        # 信号特征全局池化
        avg_pooled = self.global_avg_pool(decoded_features).squeeze(-1)
        max_pooled = self.global_max_pool(decoded_features).squeeze(-1)
        signal_features = torch.cat([avg_pooled, max_pooled], dim=1)
        
        # 时刻点处理
        timepoint_features = self.timepoint_processor(timepoint_data)
        
        # 多尺度融合
        fused_features = self.multi_scale_fusion(signal_features, timepoint_features)
        
        # 临床特征融合
        if self.clinical_feature_dim > 0 and clinical_features.numel() > 0:
            clinical_features = self.clinical_processor(clinical_features)
            fused_features = torch.cat([fused_features, clinical_features], dim=1)
        
        # 最终融合
        final_features = self.final_fusion(fused_features)
        
        # 多任务预测
        risk_scores = self.risk_head(final_features)
        conclusion_logits = self.conclusion_head(final_features)
        
        return {
            'risk_scores': risk_scores,
            'conclusion_logits': conclusion_logits,
            'fused_features': final_features,
            'signal_features': signal_features,
            'timepoint_features': timepoint_features
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        total_params = sum(p.numel() for p in self.parameters())
        return {
            'architecture': 'MultiModal42ChannelUNet',
            'input_channels': 42,
            'signal_channels': 36,
            'timepoint_channels': 6,
            'total_parameters': total_params,
            'clinical_feature_dim': self.clinical_feature_dim,
            'optimization_features': [
                'unet_encoder_decoder',
                'multi_scale_fusion',
                'dual_branch_processing',
                'anti_overfitting_techniques',
                'residual_connections',
                'global_pooling_strategies'
            ]
        }


def create_multimodal_unet_model(config: Dict[str, Any], clinical_feature_dim: int = 0) -> MultiModal42ChannelUNet:
    """创建42通道多模态UNet模型"""
    return MultiModal42ChannelUNet(config, clinical_feature_dim)