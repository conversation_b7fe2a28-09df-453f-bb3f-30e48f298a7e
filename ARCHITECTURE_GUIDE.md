# MCG深度学习项目架构指南

本项目已成功实现了一个模块化的深度学习框架，支持多种模型架构用于心磁图（MCG）数据分析。

## 概述

该项目包含了完整的多任务学习解决方案，用于预测冠状动脉狭窄风险评分和诊断结论。系统支持三种主要的深度学习架构：

1. **CNN+GRU** - 传统卷积神经网络与门控循环单元组合
2. **UNet+LSTM** - U-Net编码解码架构与长短期记忆网络
3. **Vision Transformer (ViT)** - 基于注意力机制的视觉变换器

## 模型工厂系统

### 核心特性

- **模块化设计**: 每种架构都有独立的配置文件
- **统一接口**: 所有模型都支持相同的前向传播接口
- **灵活配置**: 支持动态模型选择和超参数调整
- **扩展性**: 易于添加新的模型架构

### 使用方法

#### 1. 配置文件选择

在主配置文件 `configs/config.yaml` 中设置模型配置路径：

```yaml
model:
  model_config_path: "configs/models/cnn_gru.yaml"  # 可选: cnn_gru.yaml, unet.yaml, vit.yaml
```

#### 2. 直接使用模型工厂

```python
from src.models.model_factory import create_model_from_config

# 创建模型
model = create_model_from_config(
    model_config_path='configs/models/vit.yaml',
    input_shape=(2000, 1, 6, 6),  # (序列长度, 通道数, 高度, 宽度)
    clinical_features_dim=12
)

# 前向传播
outputs = model(mcg_data, clinical_features)
# 输出: {'risk_scores': tensor, 'conclusion_logits': tensor}
```

## 支持的模型架构

### 1. CNN+GRU (传统架构)

**配置文件**: `configs/models/cnn_gru.yaml`

**特点**:
- 参数量: ~664K
- 训练速度: 快
- 内存占用: 低
- 适用场景: 基线模型，快速原型

**架构组成**:
```
输入 (B, T, 1, 6, 6) 
  ↓
CNN空间特征提取器 
  ↓
GRU时序建模 
  ↓
临床特征融合 
  ↓
多任务预测头
```

### 2. UNet+LSTM

**配置文件**: `configs/models/unet.yaml`

**特点**:
- 参数量: ~845K
- 训练速度: 中等
- 内存占用: 中等
- 适用场景: 需要保留空间细节的应用

**架构组成**:
```
输入 (B, T, 1, 6, 6) 
  ↓
UNet编码器-解码器 (含跳跃连接)
  ↓
LSTM时序建模 
  ↓
临床特征融合 
  ↓
多任务预测头
```

### 3. Vision Transformer (ViT)

**配置文件**: `configs/models/vit.yaml`

**特点**:
- 参数量: ~2.1M
- 训练速度: 慢
- 内存占用: 高
- 适用场景: 复杂模式识别，高精度要求

**架构组成**:
```
输入 (B, T, 1, 6, 6) 
  ↓
Patch嵌入 (2x2 patches)
  ↓
位置编码 + CLS Token
  ↓
多层Transformer编码器
  ↓
时序Transformer建模
  ↓
交叉注意力特征融合
  ↓
多任务预测头
```

## 数据增强系统

### 真实数据扩增

系统支持真实的数据扩增（而非仅添加噪声）：

```yaml
augmentation:
  enabled: true
  augmentation_factor: 2  # 每个原始样本生成1个增强样本
  noise_std: 0.1
  mixup_alpha: 0.2
```

**工作原理**:
- 原始数据集大小: N
- 增强后数据集大小: N × augmentation_factor
- 每个epoch会看到不同的增强版本

### 三支血管评估指标

**LM (左主干), LAD (前降支), LCX (左回旋支), RCA (右冠状动脉)**

计算方法：
- 二元分类阈值: 0.5
- ACC = (TP + TN) / (TP + TN + FP + FN)
- 三支ACC为各血管ACC的平均值

## 训练配置

### 批量大小建议

| 模型架构 | 推荐批量大小 | GPU内存需求 |
|---------|-------------|------------|
| CNN+GRU | 32 | 4GB+ |
| UNet+LSTM | 16 | 6GB+ |
| ViT | 8 | 8GB+ |

### 训练命令

```bash
# 使用CNN+GRU架构
python train.py --config configs/config.yaml

# 或者直接指定不同配置
# 修改config.yaml中的model_config_path然后运行
```

## 性能比较

### 参数量对比
- **CNN+GRU**: 664,037 参数
- **UNet+LSTM**: 844,997 参数  
- **ViT**: 2,130,021 参数

### 训练速度对比 (相对)
- **CNN+GRU**: 1.0x (基准)
- **UNet+LSTM**: 0.8x
- **ViT**: 0.5x

### 适用场景建议

- **快速原型和基线**: CNN+GRU
- **空间细节重要**: UNet+LSTM  
- **最高精度需求**: ViT

## 扩展指南

### 添加新架构

1. 创建模型实现文件 `src/models/new_model.py`
2. 创建配置文件 `configs/models/new_model.yaml`
3. 在 `model_factory.py` 中添加创建逻辑
4. 实现 `get_model_info()` 方法以保持接口一致

### 自定义配置

所有模型配置都支持以下通用结构：

```yaml
model_type: "your_model_type"
spatial_extractor: {...}
temporal_model: {...}
clinical_processor: {...}
fusion_layer: {...}
prediction_heads: {...}
```

## 测试验证

运行完整架构测试：
```bash
python test_architectures.py
```

这将验证所有三种架构的：
- 模型创建
- 前向传播
- 输出形状正确性

## 总结

本项目提供了一个完整、灵活、可扩展的深度学习框架，支持多种先进的神经网络架构用于MCG数据分析。通过模型工厂模式，用户可以轻松切换不同架构，并根据具体需求进行配置调优。