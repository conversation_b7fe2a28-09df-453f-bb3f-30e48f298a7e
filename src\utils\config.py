import yaml
import os
from typing import Dict, Any
from dataclasses import dataclass, field


@dataclass
class DataConfig:
    xlsx_file: str
    txt_data_dir: str
    max_sequence_length: int
    train_sheet: str
    test_sheet: str
    vessel_info_sheet: str
    timepoint_file: str = None  # 新增时刻点文件路径
    # 可配置的血管目标字段
    vessel_targets: Dict[str, Any] = field(default_factory=lambda: {
        'vessels': ['LM', 'LAD', 'LCX', 'RCA'],  # 4支血管 (可改为3支: ['LAD', 'LCX', 'RCA'])
        'conclusion_field': '造影结论',  # 可配置结论字段名
        'use_three_vessel': False  # 是否使用3支血管 (排除LM)
    })
    # 时刻点高斯平滑参数
    timepoint_gaussian: Dict[str, Any] = field(default_factory=lambda: {
        'sigma': 20.0,  # 高斯核标准差
        'enabled': True  # 是否启用高斯平滑
    })
    preprocessing: Dict[str, Any] = field(default_factory=dict)
    augmentation: Dict[str, Any] = field(default_factory=dict)
    spatial: Dict[str, Any] = field(default_factory=lambda: {
        'enabled': False,
        'upsample_to_36x36': False,
        'preserve_6x6': True
    })


@dataclass
class ModelConfig:
    architecture: str
    model_config_path: str = None  # 模型配置文件路径 (支持 models/xxx.yaml)
    # 42通道模型配置字段 (兼容)
    signal_cnn: Dict[str, Any] = field(default_factory=dict)
    signal_unet: Dict[str, Any] = field(default_factory=dict)  
    signal_vit: Dict[str, Any] = field(default_factory=dict)
    timepoint: Dict[str, Any] = field(default_factory=dict)
    timepoint_unet: Dict[str, Any] = field(default_factory=dict)
    timepoint_vit: Dict[str, Any] = field(default_factory=dict)
    gru: Dict[str, Any] = field(default_factory=dict)
    fusion: Dict[str, Any] = field(default_factory=dict)
    clinical: Dict[str, Any] = field(default_factory=dict)
    final_fusion: Dict[str, Any] = field(default_factory=dict)
    output: Dict[str, Any] = field(default_factory=dict)
    # 向后兼容字段
    cnn: Dict[str, Any] = field(default_factory=dict)
    rnn: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TrainingConfig:
    batch_size: int
    num_epochs: int
    learning_rate: float
    weight_decay: float
    gradient_accumulation_steps: int = 1  # 梯度累积步数
    grad_clip_norm: float = None  # 梯度裁剪阈值
    optimizer: Dict[str, Any] = field(default_factory=dict)
    scheduler: Dict[str, Any] = field(default_factory=dict)
    loss_weights: Dict[str, Any] = field(default_factory=dict)
    early_stopping: Dict[str, Any] = field(default_factory=dict)
    checkpoint: Dict[str, Any] = field(default_factory=dict)


@dataclass
class EvaluationConfig:
    metrics: list
    visualization: Dict[str, Any] = field(default_factory=dict)


@dataclass
class HardwareConfig:
    device: str
    num_workers: int
    pin_memory: bool


@dataclass
class LoggingConfig:
    log_dir: str
    tensorboard_dir: str
    checkpoint_dir: str
    log_level: str
    save_predictions: bool


@dataclass
class Config:
    data: DataConfig
    model: ModelConfig
    training: TrainingConfig
    evaluation: EvaluationConfig
    hardware: HardwareConfig
    logging: LoggingConfig


def load_config(config_path: str) -> Config:
    """Load configuration from YAML file."""
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config_dict = yaml.safe_load(f)
    
    # Create config dataclass instances
    data_config = DataConfig(**config_dict['data'])
    model_config = ModelConfig(**config_dict['model'])
    training_config = TrainingConfig(**config_dict['training'])
    evaluation_config = EvaluationConfig(**config_dict['evaluation'])
    hardware_config = HardwareConfig(**config_dict['hardware'])
    logging_config = LoggingConfig(**config_dict['logging'])
    
    return Config(
        data=data_config,
        model=model_config,
        training=training_config,
        evaluation=evaluation_config,
        hardware=hardware_config,
        logging=logging_config
    )


def save_config(config: Config, config_path: str) -> None:
    """Save configuration to YAML file."""
    config_dict = {
        'data': config.data.__dict__,
        'model': config.model.__dict__,
        'training': config.training.__dict__,
        'evaluation': config.evaluation.__dict__,
        'hardware': config.hardware.__dict__,
        'logging': config.logging.__dict__
    }
    
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.safe_dump(config_dict, f, default_flow_style=False, allow_unicode=True)


def update_config(config: Config, updates: Dict[str, Any]) -> Config:
    """Update configuration with new values."""
    def update_nested_dict(original: dict, updates: dict) -> dict:
        for key, value in updates.items():
            if isinstance(value, dict) and key in original:
                if isinstance(original[key], dict):
                    original[key].update(value)
                else:
                    original[key] = value
            else:
                original[key] = value
        return original
    
    config_dict = {
        'data': config.data.__dict__,
        'model': config.model.__dict__,
        'training': config.training.__dict__,
        'evaluation': config.evaluation.__dict__,
        'hardware': config.hardware.__dict__,
        'logging': config.logging.__dict__
    }
    
    updated_dict = update_nested_dict(config_dict, updates)
    
    return Config(
        data=DataConfig(**updated_dict['data']),
        model=ModelConfig(**updated_dict['model']),
        training=TrainingConfig(**updated_dict['training']),
        evaluation=EvaluationConfig(**updated_dict['evaluation']),
        hardware=HardwareConfig(**updated_dict['hardware']),
        logging=LoggingConfig(**updated_dict['logging'])
    )