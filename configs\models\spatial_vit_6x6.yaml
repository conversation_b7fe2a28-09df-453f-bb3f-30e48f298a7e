# 空间感知ViT模型配置 (6x6空间处理)

# 模型架构类型
architecture: "multimodal_vit"

# 空间处理配置
spatial:
  enabled: true  # 启用空间感知处理
  upsample_to_36x36: false  # 使用原始6x6
  preserve_6x6: true  # 保持6x6空间结构

# 信号Vision Transformer配置
signal_vit:
  embed_dim: 768
  num_layers: 12
  num_heads: 12
  patch_size: 16
  dropout: 0.1
  drop_path_rate: 0.05

# 时刻点Vision Transformer配置
timepoint_vit:
  embed_dim: 384
  num_layers: 6
  num_heads: 6
  patch_size: 32
  dropout: 0.1

# 跨模态Transformer融合
fusion:
  embed_dim: 512
  num_heads: 8
  dropout: 0.1

# 临床特征处理
clinical:
  hidden_dim: 128
  dropout: 0.3

# 最终特征融合
final_fusion:
  hidden_dim: 512
  dropout: 0.3

# 多任务输出头
output:
  risk_head_hidden: 256
  conclusion_head_hidden: 128
  dropout: 0.4