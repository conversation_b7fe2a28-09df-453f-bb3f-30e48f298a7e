import os
import torch
import pandas as pd
import numpy as np
from torch.utils.data import Dataset, DataLoader
from typing import Dict, Tuple, Optional, List, Any
import logging
from .loader import load_txt_data, MCGDataLoader, extract_timepoint_features
from .multimodal_loader import MultimodalMCGDataProcessor, extract_multimodal_features
from .preprocessing import MCGPreprocessor, DataAugmentor

logger = logging.getLogger(__name__)


class MCGDataset(Dataset):
    """Custom PyTorch Dataset for MCG data."""
    
    def __init__(self, dataframe: pd.DataFrame, txt_data_dir: str, preprocessor: MCGPreprocessor,
                 augmentor: Optional[DataAugmentor] = None, is_training: bool = True, 
                 augmentation_factor: int = 2, timepoint_gaussian: dict = None, 
                 conclusion_field: str = "造影结论"):
        """
        Initialize MCG Dataset.
        
        Args:
            dataframe: DataFrame containing sample information and labels
            txt_data_dir: Directory containing .txt MCG signal files
            preprocessor: MCG data preprocessor
            augmentor: Data augmentor (optional)
            is_training: Whether this is training data (affects augmentation)
            augmentation_factor: How many augmented versions per original sample (training only)
        """
        self.original_df = dataframe.reset_index(drop=True)
        self.timepoint_gaussian = timepoint_gaussian or {'sigma': 20.0, 'enabled': True}
        self.txt_data_dir = txt_data_dir
        self.preprocessor = preprocessor
        self.augmentor = augmentor
        self.is_training = is_training
        self.augmentation_factor = augmentation_factor if is_training and augmentor else 1
        
        # Create expanded dataframe for training with augmentation
        if self.is_training and self.augmentor and augmentation_factor > 1:
            expanded_data = []
            for idx in range(len(self.original_df)):
                # Add original sample
                original_row = self.original_df.iloc[idx].copy()
                original_row['augmentation_type'] = 'original'
                original_row['original_index'] = idx
                expanded_data.append(original_row)
                
                # Add augmented samples
                for aug_idx in range(1, augmentation_factor):
                    aug_row = self.original_df.iloc[idx].copy()
                    aug_row['augmentation_type'] = f'augmented_{aug_idx}'
                    aug_row['original_index'] = idx
                    expanded_data.append(aug_row)
            
            self.df = pd.DataFrame(expanded_data).reset_index(drop=True)
            logger.info(f"Expanded training dataset from {len(self.original_df)} to {len(self.df)} samples (factor: {augmentation_factor})")
        else:
            self.df = self.original_df
            # Add augmentation_type column for consistency
            self.df = self.df.copy()
            self.df['augmentation_type'] = 'original'
            self.df['original_index'] = self.df.index
        
        # Identify required columns
        self.risk_score_columns = ['LM_risk_score', 'LAD_risk_score', 'LCX_risk_score', 'RCA_risk_score']
        self.conclusion_column = conclusion_field
        
        # Identify clinical feature columns
        self.clinical_feature_columns = [col for col in self.df.columns if col.startswith('clinical_feature_')]
        
        # Validate data
        self._validate_data()
        
        logger.info(f"Initialized MCGDataset with {len(self.df)} samples")
        logger.info(f"Clinical features: {len(self.clinical_feature_columns)} dimensions")
        
    def _validate_data(self):
        """Validate that required columns exist."""
        missing_risk_columns = [col for col in self.risk_score_columns if col not in self.df.columns]
        if missing_risk_columns:
            logger.warning(f"Missing risk score columns: {missing_risk_columns}")
        
        if self.conclusion_column not in self.df.columns:
            logger.warning(f"Missing conclusion column: {self.conclusion_column}")
        
        if not self.clinical_feature_columns:
            logger.warning("No clinical features found")
    
    def __len__(self) -> int:
        return len(self.df)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        Get a single sample.
        
        Args:
            idx: Sample index
            
        Returns:
            Dictionary containing:
            - 'mcg_data': Preprocessed MCG data tensor
            - 'clinical_features': Clinical features tensor
            - 'risk_scores': Risk scores tensor (4 values)
            - 'conclusion': Conclusion label tensor (1 value)
            - 'mcg_id': MCG ID string
        """
        try:
            # Get sample info
            sample = self.df.iloc[idx]
            mcg_id = sample['心磁号']
            
            # Load MCG data
            mcg_file_path = os.path.join(self.txt_data_dir, f"{mcg_id}.txt")
            mcg_data = load_txt_data(mcg_file_path)  # Shape: (6, 6, n_timestamp)
            
            # 强制转换所有数据为42通道格式 (36信号通道 + 6独立时刻点通道)
            timepoint_columns = ['Qp', 'Rp', 'Sp', 'To', 'Tp', 'Te']
            
            # 获取时刻点信息
            timepoint_info = {}
            for col in timepoint_columns:
                if col in sample and pd.notna(sample[col]):
                    timepoint_info[col] = sample[col]
            
            # 如果没有时刻点信息，使用默认值
            if not timepoint_info:
                # 基于序列长度的默认时刻点位置
                seq_len = mcg_data.shape[-1] if len(mcg_data.shape) > 2 else 1000
                timepoint_info = {
                    'Qp': int(seq_len * 0.25),
                    'Rp': int(seq_len * 0.28), 
                    'Sp': int(seq_len * 0.30),
                    'To': int(seq_len * 0.44),
                    'Tp': int(seq_len * 0.47),
                    'Te': int(seq_len * 0.50)
                }
            
            # 统一使用42通道多模态格式 (36归一化信号 + 6独立高斯时刻点通道)
            # 注意：此处已经包含了信号通道的归一化步骤
            gaussian_config = self.timepoint_gaussian
            mcg_data = extract_multimodal_features(mcg_data, timepoint_info, gaussian_config, normalize_signals=True)
            
            # 跳过预处理器的归一化，因为42通道数据中信号已经归一化
            # 只进行必要的格式转换和填充
            preprocessed_mcg = self.preprocessor.pad_and_format_only(mcg_data)
            
            # Apply data augmentation based on sample type
            if self.is_training and self.augmentor is not None:
                sample_aug_type = sample.get('augmentation_type', 'original')
                if sample_aug_type != 'original':
                    # For augmented samples, apply deterministic augmentation based on augmentation type
                    if 'augmented_1' in sample_aug_type:
                        preprocessed_mcg = self.augmentor.add_noise(preprocessed_mcg, noise_type='gaussian')
                    elif 'augmented_2' in sample_aug_type:
                        preprocessed_mcg = self.augmentor.add_noise(preprocessed_mcg, noise_type='sinusoidal')
                    elif 'augmented_3' in sample_aug_type:
                        preprocessed_mcg = self.augmentor.random_crop(preprocessed_mcg, crop_ratio=0.9)
                    else:
                        # For higher augmentation factors, combine techniques
                        preprocessed_mcg = self.augmentor.add_noise(preprocessed_mcg, noise_type=np.random.choice(['gaussian', 'sinusoidal']))
                        if np.random.random() < 0.3:
                            preprocessed_mcg = self.augmentor.random_crop(preprocessed_mcg, crop_ratio=0.95)
            
            # Convert to PyTorch format: (sequence_length, 1, 6, 6)
            mcg_tensor = self.preprocessor.to_pytorch_format(preprocessed_mcg)
            
            # Get clinical features
            if self.clinical_feature_columns:
                clinical_features = sample[self.clinical_feature_columns].values.astype(np.float32)
                # Handle any remaining NaN values - use -1 instead of 0
                clinical_features = np.nan_to_num(clinical_features, nan=-1.0)
            else:
                clinical_features = np.array([], dtype=np.float32)
            
            clinical_tensor = torch.FloatTensor(clinical_features)
            
            # Get risk scores
            risk_scores = []
            for col in self.risk_score_columns:
                if col in sample:
                    score = sample[col]
                    # Handle NaN values
                    if pd.isna(score):
                        score = 0.0
                    risk_scores.append(float(score))
                else:
                    risk_scores.append(0.0)
            
            risk_tensor = torch.FloatTensor(risk_scores)
            
            # Get conclusion
            if self.conclusion_column in sample:
                conclusion = sample[self.conclusion_column]
                if pd.isna(conclusion):
                    conclusion = 0.0
                conclusion = float(conclusion)
            else:
                conclusion = 0.0
            
            conclusion_tensor = torch.FloatTensor([conclusion])
            
            return {
                'mcg_data': mcg_tensor,
                'clinical_features': clinical_tensor,
                'risk_scores': risk_tensor,
                'conclusion': conclusion_tensor,
                'mcg_id': mcg_id
            }
            
        except Exception as e:
            logger.error(f"Error loading sample {idx} (MCG ID: {sample.get('心磁号', 'unknown')}): {str(e)}")
            # Return zero tensors as fallback
            return self._get_fallback_sample(idx)
    
    def _get_fallback_sample(self, idx: int) -> Dict[str, torch.Tensor]:
        """Get fallback sample with zero tensors when loading fails."""
        sample = self.df.iloc[idx]
        mcg_id = sample.get('心磁号', f'fallback_{idx}')
        
        # Check if sample has timepoint data to determine format
        timepoint_columns = ['Qp', 'Rp', 'Sp', 'To', 'Tp', 'Te']
        has_timepoints = all(col in sample for col in timepoint_columns)
        
        # Create zero tensors with correct shapes based on data format
        if has_timepoints:
            # 37-channel format: (sequence_length, 37)
            mcg_tensor = torch.zeros(self.preprocessor.max_sequence_length, 37)
        else:
            # Original 6x6 spatial format: (sequence_length, 1, 6, 6)
            mcg_tensor = torch.zeros(self.preprocessor.max_sequence_length, 1, 6, 6)
        
        clinical_tensor = torch.zeros(len(self.clinical_feature_columns))
        risk_tensor = torch.zeros(4)
        
        # But use real labels if available
        if self.conclusion_column in sample:
            conclusion = sample[self.conclusion_column]
            if pd.isna(conclusion):
                conclusion = 0.0
            conclusion = float(conclusion)
        else:
            conclusion = 0.0
        conclusion_tensor = torch.FloatTensor([conclusion])
        
        return {
            'mcg_data': mcg_tensor,
            'clinical_features': clinical_tensor,
            'risk_scores': risk_tensor,
            'conclusion': conclusion_tensor,
            'mcg_id': mcg_id
        }
    
    def get_sample_info(self, idx: int) -> Dict[str, Any]:
        """Get information about a sample without loading the data."""
        sample = self.df.iloc[idx]
        return {
            'mcg_id': sample['心磁号'],
            'split': sample.get('split', 'unknown'),
            'has_risk_scores': all(col in sample for col in self.risk_score_columns),
            'has_conclusion': self.conclusion_column in sample,
            'clinical_features_count': len(self.clinical_feature_columns)
        }


def collate_fn(batch: List[Dict[str, torch.Tensor]]) -> Dict[str, torch.Tensor]:
    """
    Custom collate function for batching MCG data.
    
    Args:
        batch: List of sample dictionaries
        
    Returns:
        Batched dictionary
    """
    # Separate components
    mcg_data = torch.stack([item['mcg_data'] for item in batch])
    clinical_features = torch.stack([item['clinical_features'] for item in batch])
    risk_scores = torch.stack([item['risk_scores'] for item in batch])
    conclusions = torch.stack([item['conclusion'] for item in batch])
    mcg_ids = [item['mcg_id'] for item in batch]
    
    return {
        'mcg_data': mcg_data,
        'clinical_features': clinical_features,
        'risk_scores': risk_scores,
        'conclusion': conclusions,
        'mcg_ids': mcg_ids
    }


def create_data_loaders(config: Dict[str, Any], data_loader: MCGDataLoader, 
                       preprocessor: Optional['MCGPreprocessor'] = None) -> Tuple[DataLoader, DataLoader]:
    """
    Create training and validation data loaders.
    
    Args:
        config: Configuration dictionary
        data_loader: MCGDataLoader instance with processed data
        preprocessor: Optional pre-fitted preprocessor to use
        
    Returns:
        Tuple of (train_loader, test_loader)
    """
    from .preprocessing import create_preprocessing_pipeline, create_augmentor
    
    # Get train/test split
    train_df, test_df = data_loader.get_train_test_split()
    
    # Use provided preprocessor or create new one
    if preprocessor is None:
        preprocessor = create_preprocessing_pipeline(config.data)
        logger.info("Created new preprocessor for data loaders")
    else:
        logger.info("Using provided preprocessor for data loaders")
    
    # Create augmentor for training
    augmentor = create_augmentor(config.data)
    
    # Get augmentation factor from config
    augmentation_factor = getattr(config.data.augmentation, 'augmentation_factor', 2) if augmentor else 1
    
    # Get timepoint gaussian config
    timepoint_gaussian = getattr(config.data, 'timepoint_gaussian', None)
    
    # Get conclusion field from config
    conclusion_field = getattr(config.data.vessel_targets, 'conclusion_field', '造影结论') if hasattr(config.data, 'vessel_targets') else '造影结论'
    
    # Create datasets
    train_dataset = MCGDataset(
        dataframe=train_df,
        txt_data_dir=config.data.txt_data_dir,
        preprocessor=preprocessor,
        augmentor=augmentor,
        is_training=True,
        augmentation_factor=augmentation_factor,
        timepoint_gaussian=timepoint_gaussian,
        conclusion_field=conclusion_field
    )
    
    test_dataset = MCGDataset(
        dataframe=test_df,
        txt_data_dir=config.data.txt_data_dir,
        preprocessor=preprocessor,
        augmentor=None,  # No augmentation for test
        is_training=False,
        augmentation_factor=1,
        timepoint_gaussian=timepoint_gaussian,
        conclusion_field=conclusion_field
    )
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        num_workers=config.hardware.num_workers,
        pin_memory=config.hardware.pin_memory,
        collate_fn=collate_fn,
        drop_last=True  # Drop last incomplete batch for stable training
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        num_workers=config.hardware.num_workers,
        pin_memory=config.hardware.pin_memory,
        collate_fn=collate_fn,
        drop_last=False
    )
    
    logger.info(f"Created train loader with {len(train_loader)} batches ({len(train_dataset)} samples)")
    logger.info(f"Created test loader with {len(test_loader)} batches ({len(test_dataset)} samples)")
    
    return train_loader, test_loader


class MCGDataModule:
    """Data module for organizing MCG data loading and preprocessing."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize MCG data module.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.data_loader = None
        self.train_loader = None
        self.test_loader = None
        self.preprocessor = None
        
    def setup(self):
        """Setup data loaders and preprocessors."""
        # Initialize data loader
        timepoint_file = getattr(self.config.data, 'timepoint_file', None)
        vessel_targets = getattr(self.config.data, 'vessel_targets', None)
        timepoint_gaussian = getattr(self.config.data, 'timepoint_gaussian', None)
        
        self.data_loader = MCGDataLoader(
            excel_path=self.config.data.xlsx_file,
            txt_data_dir=self.config.data.txt_data_dir,
            train_sheet=self.config.data.train_sheet,
            test_sheet=self.config.data.test_sheet,
            vessel_info_sheet=self.config.data.vessel_info_sheet,
            timepoint_file=timepoint_file,
            vessel_targets=vessel_targets,
            timepoint_gaussian=timepoint_gaussian
        )
        
        # Load and process data
        merged_df = self.data_loader.load_and_process_data()
        logger.info(f"Loaded total dataset: {len(merged_df)} samples")
        
        # Create and fit preprocessor first
        from .preprocessing import create_preprocessing_pipeline
        self.preprocessor = create_preprocessing_pipeline(self.config.data)
        
        # Fit preprocessor on training data subset if using training set normalization
        if self.preprocessor.use_training_set_stats:
            logger.info("Fitting preprocessor on training data subset...")
            train_df = self.data_loader.get_train_data()
            fit_preprocessor_on_subset(
                self.preprocessor, 
                train_df, 
                self.config.data.txt_data_dir, 
                max_samples=len(train_df)  # Use all training samples
            )
        
        # Create data loaders with fitted preprocessor
        self.train_loader, self.test_loader = create_data_loaders(
            self.config, self.data_loader, self.preprocessor
        )
        
    def get_train_loader(self) -> DataLoader:
        """Get training data loader."""
        if self.train_loader is None:
            raise ValueError("Data module not setup. Call setup() first.")
        return self.train_loader
    
    def get_test_loader(self) -> DataLoader:
        """Get test data loader."""
        if self.test_loader is None:
            raise ValueError("Data module not setup. Call setup() first.")
        return self.test_loader
    
    def get_sample_batch(self) -> Dict[str, torch.Tensor]:
        """Get a sample batch for model development and testing."""
        if self.train_loader is None:
            raise ValueError("Data module not setup. Call setup() first.")
        
        for batch in self.train_loader:
            return batch
    
    def get_data_info(self) -> Dict[str, Any]:
        """Get information about the dataset."""
        if self.data_loader is None:
            raise ValueError("Data module not setup. Call setup() first.")
        
        train_df, test_df = self.data_loader.get_train_test_split()
        
        info = {
            'total_samples': len(train_df) + len(test_df),
            'train_samples': len(train_df),
            'test_samples': len(test_df),
            'clinical_features_dim': len([col for col in train_df.columns if col.startswith('clinical_feature_')]),
            'max_sequence_length': self.config.data.max_sequence_length,
            'spatial_dims': (6, 6),
            'num_risk_targets': 4,
            'num_conclusion_targets': 1
        }
        
        return info


def fit_preprocessor_on_subset(preprocessor: MCGPreprocessor, train_df: pd.DataFrame, txt_data_dir: str, max_samples: int = 50):
    """
    在训练数据的子集上预拟合预处理器
    
    Args:
        preprocessor: MCG预处理器
        train_df: 训练数据DataFrame
        txt_data_dir: MCG文件目录
        max_samples: 用于拟合的最大样本数
    """
    import os
    import numpy as np
    
    logger.info(f"使用{min(max_samples, len(train_df))}个训练样本预拟合预处理器...")
    
    if not preprocessor.use_training_set_stats:
        logger.info("使用样本内归一化，无需预拟合")
        return
    
    # 收集一些样本进行拟合
    mcg_samples = []
    valid_samples = 0
    
    for idx, row in train_df.iterrows():
        if valid_samples >= max_samples:
            break
            
        mcg_id = row['心磁号']
        mcg_file_path = os.path.join(txt_data_dir, f"{mcg_id}.txt")
        
        if os.path.exists(mcg_file_path):
            try:
                mcg_data = load_txt_data(mcg_file_path)
                # Pad the sequence to max length for consistent statistics
                padded_data = preprocessor.pad_sequence(mcg_data)
                mcg_samples.append(padded_data)
                valid_samples += 1
            except Exception as e:
                logger.warning(f"加载样本 {mcg_id} 失败: {e}")
                continue
    
    if mcg_samples:
        # Stack samples into batch format: (batch_size, 6, 6, seq_len)
        training_batch = np.stack(mcg_samples, axis=0)
        
        # Fit normalization statistics on this batch
        _ = preprocessor.normalize_data(training_batch, fit=True)
        
        logger.info(f"预处理器已在{len(mcg_samples)}个训练样本上拟合完成")
    else:
        logger.warning("没有有效样本用于拟合预处理器")