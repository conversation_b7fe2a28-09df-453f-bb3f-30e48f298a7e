"""
配置访问辅助函数
"""

def safe_get(config, key, default=None):
    """
    安全获取配置值，支持字典和对象两种形式
    
    Args:
        config: 配置对象或字典
        key: 键名
        default: 默认值
        
    Returns:
        配置值或默认值
    """
    if hasattr(config, key):
        return getattr(config, key)
    elif isinstance(config, dict) and key in config:
        return config[key]
    else:
        return default

def safe_get_nested(config, keys, default=None):
    """
    安全获取嵌套配置值
    
    Args:
        config: 配置对象或字典
        keys: 键名列表，如 ['fusion', 'dropout']
        default: 默认值
        
    Returns:
        配置值或默认值
    """
    current = config
    for key in keys:
        current = safe_get(current, key, {})
        if current == {}:
            return default
    return current if current != {} else default