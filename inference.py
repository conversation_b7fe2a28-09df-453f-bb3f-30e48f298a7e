#!/usr/bin/env python3
"""
Inference script for MCG Deep Learning Model
"""

import os
import sys
import argparse
import logging
import torch
import pandas as pd
import numpy as np
from typing import Dict, List, Any

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils.config import load_config
from data.loader import MCGDataLoader, load_txt_data
from data.preprocessing import MCGPreprocessor
from models.mcg_model import create_mcg_model
from utils.checkpoint import CheckpointManager


def setup_logging() -> logging.Logger:
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def load_model_from_checkpoint(checkpoint_path: str, device: torch.device) -> tuple:
    """
    Load model from checkpoint.
    
    Args:
        checkpoint_path: Path to checkpoint file
        device: Device to load model on
        
    Returns:
        Tuple of (model, config, preprocessor)
    """
    logger = logging.getLogger(__name__)
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location=device)
    config_dict = checkpoint['config']
    
    # Reconstruct config (simplified)
    class SimpleConfig:
        def __init__(self, config_dict):
            for key, value in config_dict.items():
                if isinstance(value, dict):
                    setattr(self, key, SimpleConfig(value))
                else:
                    setattr(self, key, value)
    
    config = SimpleConfig(config_dict)
    
    # Determine clinical feature dimension from checkpoint
    clinical_feature_dim = 0
    for key in checkpoint['model_state_dict'].keys():
        if 'clinical_processor' in key and 'processor.0.weight' in key:
            clinical_feature_dim = checkpoint['model_state_dict'][key].shape[1]
            break
    
    # Create model
    model = create_mcg_model(config.model.__dict__, clinical_feature_dim)
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    # Create preprocessor
    preprocessor = MCGPreprocessor(
        max_sequence_length=config.data.max_sequence_length,
        normalization=config.data.preprocessing.normalization,
        padding_strategy=config.data.preprocessing.padding_strategy
    )
    
    logger.info(f"Model loaded from {checkpoint_path}")
    logger.info(f"Clinical feature dimension: {clinical_feature_dim}")
    
    return model, config, preprocessor


def predict_single_sample(model: torch.nn.Module, mcg_data: torch.Tensor,
                         clinical_features: torch.Tensor, device: torch.device) -> Dict[str, Any]:
    """
    Make prediction for a single sample.
    
    Args:
        model: Trained model
        mcg_data: MCG data tensor
        clinical_features: Clinical features tensor
        device: Device for computation
        
    Returns:
        Dictionary with predictions
    """
    model.eval()
    
    with torch.no_grad():
        # Add batch dimension and move to device
        mcg_data = mcg_data.unsqueeze(0).to(device)
        clinical_features = clinical_features.unsqueeze(0).to(device)
        
        # Forward pass
        outputs = model(mcg_data, clinical_features)
        
        # Extract predictions
        risk_scores = outputs['risk_scores'].squeeze(0).cpu().numpy()
        conclusion_logits = outputs['conclusion_logits'].squeeze(0).cpu().numpy()
        conclusion_prob = torch.sigmoid(outputs['conclusion_logits']).squeeze(0).cpu().numpy()
        conclusion_pred = (conclusion_prob >= 0.5).astype(int)
        
        return {
            'risk_scores': {
                'LM': float(risk_scores[0]),
                'LAD': float(risk_scores[1]),
                'LCX': float(risk_scores[2]),
                'RCA': float(risk_scores[3])
            },
            'conclusion': {
                'probability': float(conclusion_prob[0]),
                'prediction': int(conclusion_pred[0]),
                'logit': float(conclusion_logits[0])
            }
        }


def predict_batch(model: torch.nn.Module, data_loader: torch.utils.data.DataLoader,
                 device: torch.device) -> List[Dict[str, Any]]:
    """
    Make predictions for a batch of samples.
    
    Args:
        model: Trained model
        data_loader: Data loader
        device: Device for computation
        
    Returns:
        List of prediction dictionaries
    """
    model.eval()
    predictions = []
    
    with torch.no_grad():
        for batch in data_loader:
            mcg_data = batch['mcg_data'].to(device)
            clinical_features = batch['clinical_features'].to(device)
            mcg_ids = batch['mcg_ids']
            
            # Forward pass
            outputs = model(mcg_data, clinical_features)
            
            # Extract predictions
            risk_scores = outputs['risk_scores'].cpu().numpy()
            conclusion_logits = outputs['conclusion_logits'].cpu().numpy()
            conclusion_probs = torch.sigmoid(outputs['conclusion_logits']).cpu().numpy()
            conclusion_preds = (conclusion_probs >= 0.5).astype(int)
            
            # Process each sample in batch
            for i in range(len(mcg_ids)):
                pred_dict = {
                    'mcg_id': mcg_ids[i],
                    'risk_scores': {
                        'LM': float(risk_scores[i, 0]),
                        'LAD': float(risk_scores[i, 1]),
                        'LCX': float(risk_scores[i, 2]),
                        'RCA': float(risk_scores[i, 3])
                    },
                    'conclusion': {
                        'probability': float(conclusion_probs[i, 0]),
                        'prediction': int(conclusion_preds[i, 0]),
                        'logit': float(conclusion_logits[i, 0])
                    }
                }
                predictions.append(pred_dict)
    
    return predictions


def save_predictions(predictions: List[Dict[str, Any]], output_path: str) -> None:
    """Save predictions to file."""
    # Convert to flat format for DataFrame
    flat_predictions = []
    
    for pred in predictions:
        flat_pred = {
            'mcg_id': pred['mcg_id'],
            'LM_risk_score': pred['risk_scores']['LM'],
            'LAD_risk_score': pred['risk_scores']['LAD'],
            'LCX_risk_score': pred['risk_scores']['LCX'],
            'RCA_risk_score': pred['risk_scores']['RCA'],
            'conclusion_probability': pred['conclusion']['probability'],
            'conclusion_prediction': pred['conclusion']['prediction'],
            'conclusion_logit': pred['conclusion']['logit']
        }
        flat_predictions.append(flat_pred)
    
    # Save as CSV
    df = pd.DataFrame(flat_predictions)
    df.to_csv(output_path, index=False)
    
    logger = logging.getLogger(__name__)
    logger.info(f"Predictions saved to {output_path}")


def main():
    parser = argparse.ArgumentParser(description="MCG Model Inference")
    parser.add_argument("--checkpoint", type=str, required=True,
                       help="Path to model checkpoint")
    parser.add_argument("--data-path", type=str, required=True,
                       help="Path to Excel file with data")
    parser.add_argument("--mcg-dir", type=str, required=True,
                       help="Directory containing MCG .txt files")
    parser.add_argument("--output", type=str, default="predictions.csv",
                       help="Output file for predictions")
    parser.add_argument("--batch-size", type=int, default=32,
                       help="Batch size for inference")
    parser.add_argument("--single-id", type=str, default=None,
                       help="MCG ID for single sample prediction")
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging()
    logger.info("Starting MCG Model Inference")
    
    # Get device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")
    
    try:
        # Load model
        logger.info(f"Loading model from {args.checkpoint}")
        model, config, preprocessor = load_model_from_checkpoint(args.checkpoint, device)
        
        if args.single_id:
            # Single sample prediction
            logger.info(f"Predicting for single sample: {args.single_id}")
            
            # Load MCG data
            mcg_file_path = os.path.join(args.mcg_dir, f"{args.single_id}.txt")
            if not os.path.exists(mcg_file_path):
                raise FileNotFoundError(f"MCG file not found: {mcg_file_path}")
            
            mcg_data = load_txt_data(mcg_file_path)  # Shape: (6, 6, n_timestamp)
            
            # Preprocess
            preprocessed_mcg = preprocessor.preprocess_single(mcg_data, fit=False)
            mcg_tensor = preprocessor.to_pytorch_format(preprocessed_mcg)
            
            # Create dummy clinical features (you may need to load actual clinical features)
            clinical_tensor = torch.zeros(config.model.fusion.clinical_feature_dim)
            
            # Predict
            prediction = predict_single_sample(model, mcg_tensor, clinical_tensor, device)
            
            # Print results
            print(f"\nPredictions for MCG ID: {args.single_id}")
            print("Risk Scores:")
            for vessel, score in prediction['risk_scores'].items():
                print(f"  {vessel}: {score:.4f}")
            
            print(f"\nConclusion:")
            print(f"  Probability: {prediction['conclusion']['probability']:.4f}")
            print(f"  Prediction: {prediction['conclusion']['prediction']}")
            
        else:
            # Batch prediction
            logger.info("Setting up data for batch prediction")
            
            # Load data
            data_loader = MCGDataLoader(
                excel_path=args.data_path,
                txt_data_dir=args.mcg_dir,
                train_sheet="训练集",
                test_sheet="内测集", 
                vessel_info_sheet="训测集三支信息"
            )
            
            merged_df = data_loader.load_and_process_data()
            logger.info(f"Loaded {len(merged_df)} samples for inference")
            
            # Create dataset and data loader
            from data.dataset import MCGDataset, collate_fn
            from torch.utils.data import DataLoader
            
            dataset = MCGDataset(
                dataframe=merged_df,
                txt_data_dir=args.mcg_dir,
                preprocessor=preprocessor,
                augmentor=None,
                is_training=False
            )
            
            inference_loader = DataLoader(
                dataset,
                batch_size=args.batch_size,
                shuffle=False,
                num_workers=2,
                collate_fn=collate_fn
            )
            
            # Make predictions
            logger.info("Making predictions...")
            predictions = predict_batch(model, inference_loader, device)
            
            # Save predictions
            save_predictions(predictions, args.output)
            
            # Print summary
            risk_scores_summary = {
                'LM': np.mean([p['risk_scores']['LM'] for p in predictions]),
                'LAD': np.mean([p['risk_scores']['LAD'] for p in predictions]),
                'LCX': np.mean([p['risk_scores']['LCX'] for p in predictions]),
                'RCA': np.mean([p['risk_scores']['RCA'] for p in predictions])
            }
            
            conclusion_summary = {
                'mean_probability': np.mean([p['conclusion']['probability'] for p in predictions]),
                'positive_predictions': sum([p['conclusion']['prediction'] for p in predictions])
            }
            
            print(f"\nInference completed for {len(predictions)} samples")
            print(f"Average risk scores: {risk_scores_summary}")
            print(f"Conclusion summary: {conclusion_summary}")
            print(f"Results saved to: {args.output}")
        
    except Exception as e:
        logger.error(f"Inference failed: {str(e)}")
        logger.exception("Full traceback:")
        raise


if __name__ == "__main__":
    main()