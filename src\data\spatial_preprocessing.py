"""
空间数据预处理 - 支持6x6空间结构和36x36上采样
"""

import numpy as np
import torch
import torch.nn.functional as F
from typing import Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


def bicubic_upsample_6x6_to_36x36(mcg_data: np.ndarray) -> np.ndarray:
    """
    使用双三次插值将6x6 MCG数据上采样到36x36
    
    Args:
        mcg_data: (6, 6, n_timestamp) 的MCG数据
        
    Returns:
        (36, 36, n_timestamp) 的上采样数据
    """
    if len(mcg_data.shape) != 3 or mcg_data.shape[0] != 6 or mcg_data.shape[1] != 6:
        raise ValueError(f"Expected (6, 6, n_timestamp) input, got {mcg_data.shape}")
    
    n_timestamp = mcg_data.shape[2]
    upsampled_data = np.zeros((36, 36, n_timestamp), dtype=np.float32)
    
    # 对每个时间点进行双三次插值
    for t in range(n_timestamp):
        frame = mcg_data[:, :, t]  # (6, 6)
        
        # 转换为PyTorch张量进行插值
        frame_tensor = torch.from_numpy(frame).unsqueeze(0).unsqueeze(0)  # (1, 1, 6, 6)
        
        # 双三次插值到36x36
        upsampled_frame = F.interpolate(
            frame_tensor,
            size=(36, 36),
            mode='bicubic',
            align_corners=True
        )
        
        upsampled_data[:, :, t] = upsampled_frame.squeeze().numpy()
    
    logger.debug(f"Bicubic upsampling: {mcg_data.shape} → {upsampled_data.shape}")
    return upsampled_data


def extract_spatial_multimodal_features(mcg_data: np.ndarray, 
                                       timepoint_info: Dict[str, int], 
                                       spatial_config: dict = None,
                                       gaussian_config: dict = None,
                                       normalize_signals: bool = True) -> Tuple[np.ndarray, np.ndarray]:
    """
    提取保持空间结构的多模态特征
    
    Args:
        mcg_data: 原始MCG数据 (6, 6, n_timestamp)
        timepoint_info: 时刻点信息
        spatial_config: 空间处理配置
        gaussian_config: 高斯平滑配置
        normalize_signals: 是否归一化信号
        
    Returns:
        spatial_data: 空间数据 (6, 6, n_timestamp) 或 (36, 36, n_timestamp)
        timepoint_data: 时刻点数据 (6, n_timestamp)
    """
    if spatial_config is None:
        spatial_config = {'upsample_to_36x36': False, 'preserve_6x6': True}
    
    if gaussian_config is None:
        gaussian_config = {'sigma': 20.0, 'enabled': True}
    
    sequence_length = mcg_data.shape[2]
    
    # 步骤1: 处理空间数据
    if spatial_config.get('upsample_to_36x36', False):
        # 上采样到36x36
        spatial_data = bicubic_upsample_6x6_to_36x36(mcg_data)
        logger.info(f"Upsampled MCG data: {mcg_data.shape} → {spatial_data.shape}")
    else:
        # 保持6x6结构
        spatial_data = mcg_data.copy()
        logger.info(f"Preserved 6x6 spatial structure: {spatial_data.shape}")
    
    # 步骤2: 归一化空间数据
    if normalize_signals:
        original_shape = spatial_data.shape
        # 对每个空间位置独立归一化
        for i in range(original_shape[0]):
            for j in range(original_shape[1]):
                channel_data = spatial_data[i, j]
                mean_val = np.mean(channel_data)
                std_val = np.std(channel_data)
                if std_val > 1e-8:
                    spatial_data[i, j] = (channel_data - mean_val) / std_val
                else:
                    spatial_data[i, j] = channel_data - mean_val
        
        logger.debug(f"Normalized spatial data with shape {spatial_data.shape}")
    
    # 步骤3: 创建时刻点通道 (保持独立)
    timepoint_data = create_timepoint_channels(timepoint_info, sequence_length, gaussian_config)
    
    logger.info(f"Created spatial-temporal features: spatial={spatial_data.shape}, timepoint={timepoint_data.shape}")
    
    return spatial_data, timepoint_data


def create_timepoint_channels(timepoints: Dict[str, int], 
                            sequence_length: int, 
                            gaussian_config: dict) -> np.ndarray:
    """
    创建时刻点通道
    
    Args:
        timepoints: 时刻点字典
        sequence_length: 序列长度
        gaussian_config: 高斯配置
        
    Returns:
        时刻点数据 (6, sequence_length)
    """
    timepoint_names = ['Qp', 'Rp', 'Sp', 'To', 'Tp', 'Te']
    timepoint_channels = np.zeros((6, sequence_length), dtype=np.float32)
    
    if gaussian_config.get('enabled', True):
        gaussian_sigma = gaussian_config.get('sigma', 20.0)
        
        for i, tp_name in enumerate(timepoint_names):
            if tp_name in timepoints:
                position = timepoints[tp_name]
                
                if 0 <= position < sequence_length:
                    x = np.arange(sequence_length, dtype=np.float32)
                    gaussian_peak = np.exp(-0.5 * ((x - position) / gaussian_sigma) ** 2)
                    timepoint_channels[i] = gaussian_peak
    else:
        # 离散脉冲
        for i, tp_name in enumerate(timepoint_names):
            if tp_name in timepoints:
                position = timepoints[tp_name]
                if 0 <= position < sequence_length:
                    timepoint_channels[i, position] = 1.0
    
    return timepoint_channels


class SpatialMCGPreprocessor:
    """
    空间感知的MCG预处理器
    """
    
    def __init__(self, max_sequence_length: int = 1600, 
                 spatial_config: dict = None,
                 gaussian_config: dict = None,
                 normalization: str = "standard"):
        """
        初始化空间预处理器
        
        Args:
            max_sequence_length: 最大序列长度
            spatial_config: 空间处理配置
            gaussian_config: 高斯配置
            normalization: 归一化方式
        """
        self.max_sequence_length = max_sequence_length
        self.spatial_config = spatial_config or {'upsample_to_36x36': False, 'preserve_6x6': True}
        self.gaussian_config = gaussian_config or {'sigma': 20.0, 'enabled': True}
        self.normalization = normalization
        
        logger.info(f"SpatialMCGPreprocessor initialized with config: {self.spatial_config}")
    
    def process_sample(self, mcg_data: np.ndarray, timepoint_info: Dict[str, int]) -> Dict[str, np.ndarray]:
        """
        处理单个MCG样本
        
        Args:
            mcg_data: (6, 6, n_timestamp) MCG数据
            timepoint_info: 时刻点信息
            
        Returns:
            包含空间数据和时刻点数据的字典
        """
        # 提取空间和时刻点特征
        spatial_data, timepoint_data = extract_spatial_multimodal_features(
            mcg_data, timepoint_info, self.spatial_config, self.gaussian_config, 
            normalize_signals=True
        )
        
        # 填充到目标长度
        spatial_data = self._pad_spatial_data(spatial_data)
        timepoint_data = self._pad_timepoint_data(timepoint_data)
        
        return {
            'spatial_data': spatial_data,
            'timepoint_data': timepoint_data,
            'spatial_shape': spatial_data.shape[:2],  # (height, width)
            'format': '36x36' if self.spatial_config.get('upsample_to_36x36', False) else '6x6'
        }
    
    def _pad_spatial_data(self, spatial_data: np.ndarray) -> np.ndarray:
        """填充空间数据到目标长度"""
        current_length = spatial_data.shape[2]
        
        if current_length >= self.max_sequence_length:
            return spatial_data[:, :, :self.max_sequence_length]
        
        # 零填充
        pad_length = self.max_sequence_length - current_length
        padded_data = np.pad(spatial_data, ((0, 0), (0, 0), (0, pad_length)), mode='constant')
        
        return padded_data
    
    def _pad_timepoint_data(self, timepoint_data: np.ndarray) -> np.ndarray:
        """填充时刻点数据到目标长度"""
        current_length = timepoint_data.shape[1]
        
        if current_length >= self.max_sequence_length:
            return timepoint_data[:, :self.max_sequence_length]
        
        # 零填充
        pad_length = self.max_sequence_length - current_length
        padded_data = np.pad(timepoint_data, ((0, 0), (0, pad_length)), mode='constant')
        
        return padded_data
    
    def to_pytorch_format(self, processed_data: Dict[str, np.ndarray]) -> Dict[str, torch.Tensor]:
        """
        转换为PyTorch格式
        
        Args:
            processed_data: 处理后的数据字典
            
        Returns:
            PyTorch张量字典
        """
        spatial_data = processed_data['spatial_data']  # (H, W, T)
        timepoint_data = processed_data['timepoint_data']  # (6, T)
        
        # 空间数据: (H, W, T) → (T, 1, H, W) 用于2D CNN
        spatial_tensor = torch.FloatTensor(spatial_data).permute(2, 0, 1).unsqueeze(1)  # (T, 1, H, W)
        
        # 时刻点数据: (6, T) → (T, 6) 用于1D CNN
        timepoint_tensor = torch.FloatTensor(timepoint_data.T)  # (T, 6)
        
        return {
            'spatial': spatial_tensor,
            'timepoint': timepoint_tensor,
            'format': processed_data['format']
        }


def test_spatial_preprocessing():
    """测试空间预处理功能"""
    print("🧪 测试空间预处理功能")
    
    # 创建测试数据
    mcg_data = np.random.randn(6, 6, 1000).astype(np.float32)
    timepoint_info = {'Qp': 250, 'Rp': 280, 'Sp': 300, 'To': 440, 'Tp': 470, 'Te': 500}
    
    # 测试6x6保持
    config_6x6 = {'upsample_to_36x36': False, 'preserve_6x6': True}
    preprocessor_6x6 = SpatialMCGPreprocessor(spatial_config=config_6x6)
    result_6x6 = preprocessor_6x6.process_sample(mcg_data, timepoint_info)
    
    print(f"6x6模式:")
    print(f"  空间数据: {result_6x6['spatial_data'].shape}")
    print(f"  时刻点数据: {result_6x6['timepoint_data'].shape}")
    print(f"  格式: {result_6x6['format']}")
    
    # 测试36x36上采样
    config_36x36 = {'upsample_to_36x36': True}
    preprocessor_36x36 = SpatialMCGPreprocessor(spatial_config=config_36x36)
    result_36x36 = preprocessor_36x36.process_sample(mcg_data, timepoint_info)
    
    print(f"\\n36x36模式:")
    print(f"  空间数据: {result_36x36['spatial_data'].shape}")
    print(f"  时刻点数据: {result_36x36['timepoint_data'].shape}")
    print(f"  格式: {result_36x36['format']}")
    
    # 转换为PyTorch格式
    pytorch_6x6 = preprocessor_6x6.to_pytorch_format(result_6x6)
    pytorch_36x36 = preprocessor_36x36.to_pytorch_format(result_36x36)
    
    print(f"\\nPyTorch格式:")
    print(f"6x6 - 空间: {pytorch_6x6['spatial'].shape}, 时刻点: {pytorch_6x6['timepoint'].shape}")
    print(f"36x36 - 空间: {pytorch_36x36['spatial'].shape}, 时刻点: {pytorch_36x36['timepoint'].shape}")


if __name__ == "__main__":
    test_spatial_preprocessing()