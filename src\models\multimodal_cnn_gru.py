"""
终极42通道多模态CNN+GRU模型
整合所有历史优化策略：分离式处理 + 防过拟合 + 跨模态融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)


class SignalCNNProcessor(nn.Module):
    """信号CNN处理器 - 整合历史优化策略"""
    
    def __init__(self, input_channels: int = 36, base_channels: int = 64, 
                 num_blocks: int = 4, dropout: float = 0.2):
        super().__init__()
        
        self.blocks = nn.ModuleList()
        in_ch = input_channels
        
        for i in range(num_blocks):
            out_ch = base_channels * (2 ** i)  # 64, 128, 256, 512
            
            # 残差CNN块 - 整合防过拟合策略
            block = nn.Sequential(
                # 第一个卷积
                nn.Conv1d(in_ch, out_ch, kernel_size=7, padding=3, bias=False),
                nn.BatchNorm1d(out_ch),
                nn.GELU(),
                nn.Dropout1d(dropout),
                
                # 第二个卷积
                nn.Conv1d(out_ch, out_ch, kernel_size=5, padding=2, bias=False),
                nn.BatchNorm1d(out_ch),
                nn.GELU(),
                nn.Dropout1d(dropout),
                
                # 下采样
                nn.MaxPool1d(2) if i < num_blocks - 1 else nn.AdaptiveAvgPool1d(1)
            )
            
            self.blocks.append(block)
            in_ch = out_ch
        
        self.output_dim = base_channels * (2 ** (num_blocks - 1))
        logger.info(f"SignalCNNProcessor: {input_channels} → {self.output_dim}, blocks={num_blocks}")
    
    def forward(self, x):
        for block in self.blocks:
            x = block(x)
        return x.squeeze(-1)  # (batch_size, output_dim)


class TimepointProcessor(nn.Module):
    """时刻点处理器 - 专门处理6个独立时刻点通道"""
    
    def __init__(self, timepoint_channels: int = 6, hidden_dim: int = 128, 
                 num_layers: int = 3, dropout: float = 0.2):
        super().__init__()
        
        layers = []
        in_ch = timepoint_channels
        
        for i in range(num_layers):
            layers.extend([
                nn.Conv1d(in_ch, hidden_dim, kernel_size=5, padding=2, bias=False),
                nn.BatchNorm1d(hidden_dim),
                nn.GELU(),
                nn.Dropout1d(dropout)
            ])
            
            if i < num_layers - 1:
                layers.extend([
                    nn.Conv1d(hidden_dim, hidden_dim, kernel_size=3, padding=1, bias=False),
                    nn.BatchNorm1d(hidden_dim),
                    nn.GELU(),
                    nn.Dropout1d(dropout)
                ])
            
            in_ch = hidden_dim
        
        layers.append(nn.AdaptiveAvgPool1d(1))
        self.processor = nn.Sequential(*layers)
        self.output_dim = hidden_dim
        
        logger.info(f"TimepointProcessor: {timepoint_channels} → {hidden_dim}, layers={num_layers}")
    
    def forward(self, x):
        return self.processor(x).squeeze(-1)


class EnhancedGRU(nn.Module):
    """增强的GRU时序编码器"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 512, num_layers: int = 3, 
                 dropout: float = 0.3, recurrent_dropout: float = 0.2):
        super().__init__()
        
        self.gru = nn.GRU(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0.0,
            batch_first=True,
            bidirectional=True
        )
        
        # 输出维度是双向的
        self.output_dim = hidden_dim * 2
        
        # 时序特征增强
        self.temporal_enhancement = nn.Sequential(
            nn.Linear(self.output_dim, self.output_dim),
            nn.LayerNorm(self.output_dim),
            nn.GELU(),
            nn.Dropout(dropout)
        )
        
        logger.info(f"EnhancedGRU: {input_dim} → {self.output_dim}, layers={num_layers}")
    
    def forward(self, x):
        # x: (batch_size, seq_len, input_dim)
        gru_out, _ = self.gru(x)
        
        # 全局平均池化和最大池化
        avg_pooled = torch.mean(gru_out, dim=1)
        max_pooled, _ = torch.max(gru_out, dim=1)
        
        # 拼接两种池化结果
        combined = avg_pooled + max_pooled
        enhanced = self.temporal_enhancement(combined)
        
        return enhanced


class CrossModalAttention(nn.Module):
    """跨模态注意力融合 - 整合历史优化策略"""
    
    def __init__(self, signal_dim: int, timepoint_dim: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        
        # 确保维度可整除
        if signal_dim % num_heads != 0:
            divisors = [i for i in range(1, signal_dim + 1) if signal_dim % i == 0]
            valid_heads = [h for h in divisors if h <= 32]
            num_heads = max(valid_heads) if valid_heads else 1
        
        self.num_heads = num_heads
        self.head_dim = signal_dim // num_heads
        
        # 多头注意力
        self.attention = nn.MultiheadAttention(
            embed_dim=signal_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # 时刻点到信号的投影
        self.timepoint_proj = nn.Linear(timepoint_dim, signal_dim)
        
        # 门控融合
        self.gate = nn.Sequential(
            nn.Linear(signal_dim + timepoint_dim, signal_dim),
            nn.Sigmoid()
        )
        
        # 特征增强
        self.enhancement = nn.Sequential(
            nn.LayerNorm(signal_dim + timepoint_dim),
            nn.Linear(signal_dim + timepoint_dim, (signal_dim + timepoint_dim) * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear((signal_dim + timepoint_dim) * 2, signal_dim + timepoint_dim),
            nn.LayerNorm(signal_dim + timepoint_dim)
        )
        
        self.output_dim = signal_dim + timepoint_dim
        logger.info(f"CrossModalAttention: signal={signal_dim}D + timepoint={timepoint_dim}D → {self.output_dim}D")
    
    def forward(self, signal_features, timepoint_features):
        batch_size = signal_features.size(0)
        
        # 投影时刻点特征
        projected_timepoint = self.timepoint_proj(timepoint_features)
        
        # 准备注意力输入
        signal_query = signal_features.unsqueeze(1)
        timepoint_kv = projected_timepoint.unsqueeze(1)
        
        # 跨模态注意力
        attended_signal, _ = self.attention(signal_query, timepoint_kv, timepoint_kv)
        attended_signal = attended_signal.squeeze(1)
        
        # 门控融合
        combined = torch.cat([attended_signal, timepoint_features], dim=1)
        gate_weights = self.gate(combined)
        gated_signal = attended_signal * gate_weights
        
        # 最终融合
        final_combined = torch.cat([gated_signal, timepoint_features], dim=1)
        enhanced_features = self.enhancement(final_combined)
        
        return enhanced_features


class MultiModal42ChannelCNNGRU(nn.Module):
    """终极42通道多模态CNN+GRU模型"""
    
    def __init__(self, config: Dict[str, Any], clinical_feature_dim: int = 0):
        super().__init__()
        
        self.config = config
        self.clinical_feature_dim = clinical_feature_dim
        
        # 信号CNN处理器配置
        signal_config = config.get('signal_cnn', {})
        self.signal_processor = SignalCNNProcessor(
            input_channels=36,
            base_channels=signal_config.get('base_channels', 64),
            num_blocks=signal_config.get('num_blocks', 4),
            dropout=signal_config.get('dropout', 0.2)
        )
        
        # 时刻点处理器配置
        timepoint_config = config.get('timepoint', {})
        self.timepoint_processor = TimepointProcessor(
            timepoint_channels=6,
            hidden_dim=timepoint_config.get('hidden_dim', 128),
            num_layers=timepoint_config.get('num_layers', 3),
            dropout=timepoint_config.get('dropout', 0.2)
        )
        
        # GRU时序编码器
        gru_config = config.get('gru', {})
        total_features = self.signal_processor.output_dim + self.timepoint_processor.output_dim
        self.gru_encoder = EnhancedGRU(
            input_dim=total_features,
            hidden_dim=gru_config.get('hidden_dim', 512),
            num_layers=gru_config.get('num_layers', 3),
            dropout=gru_config.get('dropout', 0.3)
        )
        
        # 跨模态注意力融合
        fusion_config = config.get('fusion', {})
        self.cross_attention = CrossModalAttention(
            signal_dim=self.signal_processor.output_dim,
            timepoint_dim=self.timepoint_processor.output_dim,
            num_heads=fusion_config.get('num_heads', 8),
            dropout=fusion_config.get('dropout', 0.1)
        )
        
        # 临床特征处理
        clinical_hidden = config.get('clinical', {}).get('hidden_dim', 128)
        if clinical_feature_dim > 0:
            self.clinical_processor = nn.Sequential(
                nn.Linear(clinical_feature_dim, clinical_hidden),
                nn.LayerNorm(clinical_hidden),
                nn.GELU(),
                nn.Dropout(config.get('clinical', {}).get('dropout', 0.3)),
                nn.Linear(clinical_hidden, clinical_hidden),
                nn.LayerNorm(clinical_hidden)
            )
        
        # 最终特征融合
        fusion_input_dim = (self.cross_attention.output_dim + self.gru_encoder.output_dim + 
                           (clinical_hidden if clinical_feature_dim > 0 else 0))
        
        final_hidden = config.get('final_fusion', {}).get('hidden_dim', 512)
        self.final_fusion = nn.Sequential(
            nn.Linear(fusion_input_dim, final_hidden),
            nn.LayerNorm(final_hidden),
            nn.GELU(),
            nn.Dropout(config.get('final_fusion', {}).get('dropout', 0.3)),
            nn.Linear(final_hidden, final_hidden),
            nn.LayerNorm(final_hidden),
            nn.GELU()
        )
        
        # 多任务预测头
        output_config = config.get('output', {})
        risk_hidden = output_config.get('risk_head_hidden', 256)
        self.risk_head = nn.Sequential(
            nn.Linear(final_hidden, risk_hidden),
            nn.LayerNorm(risk_hidden),
            nn.GELU(),
            nn.Dropout(output_config.get('dropout', 0.4)),
            nn.Linear(risk_hidden, 4),
            nn.Sigmoid()
        )
        
        conclusion_hidden = output_config.get('conclusion_head_hidden', 128)
        self.conclusion_head = nn.Sequential(
            nn.Linear(final_hidden, conclusion_hidden),
            nn.LayerNorm(conclusion_hidden),
            nn.GELU(),
            nn.Dropout(output_config.get('dropout', 0.4)),
            nn.Linear(conclusion_hidden, 1)
        )
        
        # 权重初始化
        self.apply(self._init_weights)
        
        # 模型信息
        total_params = sum(p.numel() for p in self.parameters())
        logger.info(f"MultiModal42ChannelCNNGRU initialized: {total_params:,} parameters")
    
    def _init_weights(self, module):
        """权重初始化"""
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight, gain=1.0)
            if module.bias is not None:
                nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Conv1d):
            nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
        elif isinstance(module, (nn.BatchNorm1d, nn.LayerNorm)):
            nn.init.ones_(module.weight)
            nn.init.zeros_(module.bias)
    
    def forward(self, multimodal_data: torch.Tensor, clinical_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        # 转换为通道优先格式
        multimodal_data = multimodal_data.transpose(1, 2)  # (batch, 42, seq_len)
        
        # 分离通道
        signal_data = multimodal_data[:, :36, :]  # 前36通道
        timepoint_data = multimodal_data[:, 36:, :]  # 后6通道
        
        # 分支处理
        signal_features = self.signal_processor(signal_data)
        timepoint_features = self.timepoint_processor(timepoint_data)
        
        # 跨模态注意力融合
        cross_modal_features = self.cross_attention(signal_features, timepoint_features)
        
        # 为GRU准备时序输入
        # 将特征重复到时序维度（简化处理）
        seq_len = multimodal_data.size(-1)
        temporal_features = torch.cat([signal_features, timepoint_features], dim=1)
        temporal_input = temporal_features.unsqueeze(1).expand(-1, min(seq_len//8, 200), -1)
        
        # GRU时序编码
        gru_features = self.gru_encoder(temporal_input)
        
        # 特征组合
        combined_features = torch.cat([cross_modal_features, gru_features], dim=1)
        
        # 临床特征融合
        if self.clinical_feature_dim > 0 and clinical_features.numel() > 0:
            clinical_features = self.clinical_processor(clinical_features)
            combined_features = torch.cat([combined_features, clinical_features], dim=1)
        
        # 最终融合
        final_features = self.final_fusion(combined_features)
        
        # 多任务预测
        risk_scores = self.risk_head(final_features)
        conclusion_logits = self.conclusion_head(final_features)
        
        return {
            'risk_scores': risk_scores,
            'conclusion_logits': conclusion_logits,
            'fused_features': final_features,
            'signal_features': signal_features,
            'timepoint_features': timepoint_features
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        total_params = sum(p.numel() for p in self.parameters())
        return {
            'architecture': 'MultiModal42ChannelCNNGRU',
            'input_channels': 42,
            'signal_channels': 36,
            'timepoint_channels': 6,
            'total_parameters': total_params,
            'clinical_feature_dim': self.clinical_feature_dim,
            'optimization_features': [
                'dual_branch_processing',
                'cross_modal_attention',
                'enhanced_gru_encoder',
                'anti_overfitting_techniques',
                'residual_connections',
                'layer_normalization'
            ]
        }


def create_multimodal_cnn_gru_model(config: Dict[str, Any], clinical_feature_dim: int = 0) -> MultiModal42ChannelCNNGRU:
    """创建42通道多模态CNN+GRU模型"""
    return MultiModal42ChannelCNNGRU(config, clinical_feature_dim)