import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import torch
from typing import Dict, List, Tuple, Any, Optional
from sklearn.metrics import confusion_matrix
import logging
import os

logger = logging.getLogger(__name__)

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


class MCGVisualizer:
    """Visualization utilities for MCG model training and evaluation."""
    
    def __init__(self, save_dir: str = "plots"):
        """
        Initialize visualizer.
        
        Args:
            save_dir: Directory to save plots
        """
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
    def plot_training_curves(self, training_history: Dict[str, List[float]], 
                           save_path: Optional[str] = None) -> plt.Figure:
        """
        Plot training and validation loss curves.
        
        Args:
            training_history: Dictionary with training history
            save_path: Path to save the plot
            
        Returns:
            Matplotlib figure
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Training Progress', fontsize=16, fontweight='bold')
        
        epochs = range(1, len(training_history['train_loss']) + 1)
        
        # Loss curves
        axes[0, 0].plot(epochs, training_history['train_loss'], 'b-', label='Train Loss', linewidth=2)
        axes[0, 0].plot(epochs, training_history['val_loss'], 'r-', label='Validation Loss', linewidth=2)
        axes[0, 0].set_title('Loss Curves')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # Learning rate
        axes[0, 1].plot(epochs, training_history['learning_rate'], 'g-', linewidth=2)
        axes[0, 1].set_title('Learning Rate Schedule')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Learning Rate')
        axes[0, 1].set_yscale('log')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Epoch time
        axes[1, 0].plot(epochs, training_history['epoch_time'], 'm-', linewidth=2)
        axes[1, 0].set_title('Training Time per Epoch')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Time (seconds)')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Loss difference
        loss_diff = np.array(training_history['val_loss']) - np.array(training_history['train_loss'])
        axes[1, 1].plot(epochs, loss_diff, 'orange', linewidth=2)
        axes[1, 1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
        axes[1, 1].set_title('Validation - Training Loss')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Loss Difference')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Training curves saved to {save_path}")
        
        return fig
    
    def plot_confusion_matrix(self, y_true: np.ndarray, y_pred: np.ndarray,
                            classes: List[str] = None, normalize: bool = False,
                            save_path: Optional[str] = None) -> plt.Figure:
        """
        Plot confusion matrix.
        
        Args:
            y_true: True labels
            y_pred: Predicted labels
            classes: Class names
            normalize: Whether to normalize the matrix
            save_path: Path to save the plot
            
        Returns:
            Matplotlib figure
        """
        # Calculate confusion matrix
        cm = confusion_matrix(y_true, y_pred)
        
        if normalize:
            cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
            title = 'Normalized Confusion Matrix'
            fmt = '.2f'
        else:
            title = 'Confusion Matrix'
            fmt = 'd'
        
        if classes is None:
            classes = ['Negative', 'Positive']
        
        # Create plot
        fig, ax = plt.subplots(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt=fmt, cmap='Blues', 
                   xticklabels=classes, yticklabels=classes, ax=ax)
        
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.set_xlabel('Predicted Label', fontsize=12)
        ax.set_ylabel('True Label', fontsize=12)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Confusion matrix saved to {save_path}")
        
        return fig
    
    def plot_roc_curves(self, detailed_metrics: Dict[str, Any], 
                       save_path: Optional[str] = None) -> plt.Figure:
        """
        Plot ROC curves.
        
        Args:
            detailed_metrics: Dictionary with ROC curve data
            save_path: Path to save the plot
            
        Returns:
            Matplotlib figure
        """
        fig, ax = plt.subplots(figsize=(8, 6))
        
        if 'roc_curve' in detailed_metrics:
            roc_data = detailed_metrics['roc_curve']
            fpr, tpr = roc_data['fpr'], roc_data['tpr']
            
            # Calculate AUC
            auc = np.trapz(tpr, fpr)
            
            ax.plot(fpr, tpr, 'b-', linewidth=2, label=f'ROC Curve (AUC = {auc:.3f})')
            ax.plot([0, 1], [0, 1], 'k--', alpha=0.5, label='Random Classifier')
            
            ax.set_xlabel('False Positive Rate', fontsize=12)
            ax.set_ylabel('True Positive Rate', fontsize=12)
            ax.set_title('Receiver Operating Characteristic (ROC) Curve', 
                        fontsize=14, fontweight='bold')
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, 'No ROC curve data available', 
                   ha='center', va='center', transform=ax.transAxes, fontsize=14)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"ROC curve saved to {save_path}")
        
        return fig
    
    def plot_precision_recall_curve(self, detailed_metrics: Dict[str, Any],
                                   save_path: Optional[str] = None) -> plt.Figure:
        """
        Plot Precision-Recall curve.
        
        Args:
            detailed_metrics: Dictionary with PR curve data
            save_path: Path to save the plot
            
        Returns:
            Matplotlib figure
        """
        fig, ax = plt.subplots(figsize=(8, 6))
        
        if 'pr_curve' in detailed_metrics:
            pr_data = detailed_metrics['pr_curve']
            precision, recall = pr_data['precision'], pr_data['recall']
            
            # Calculate average precision
            avg_precision = np.trapz(precision, recall)
            
            ax.plot(recall, precision, 'r-', linewidth=2, 
                   label=f'PR Curve (AP = {avg_precision:.3f})')
            
            ax.set_xlabel('Recall', fontsize=12)
            ax.set_ylabel('Precision', fontsize=12)
            ax.set_title('Precision-Recall Curve', fontsize=14, fontweight='bold')
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, 'No PR curve data available', 
                   ha='center', va='center', transform=ax.transAxes, fontsize=14)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"PR curve saved to {save_path}")
        
        return fig
    
    def plot_metrics_comparison(self, metrics_dict: Dict[str, float],
                              save_path: Optional[str] = None) -> plt.Figure:
        """
        Plot comparison of different metrics.
        
        Args:
            metrics_dict: Dictionary of metric values
            save_path: Path to save the plot
            
        Returns:
            Matplotlib figure
        """
        # Separate metrics by type
        risk_metrics = {k: v for k, v in metrics_dict.items() if 'risk' in k}
        conclusion_metrics = {k: v for k, v in metrics_dict.items() if 'conclusion' in k}
        vessel_metrics = {k: v for k, v in metrics_dict.items() 
                         if any(vessel in k for vessel in ['LM', 'LAD', 'LCX', 'RCA'])}
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Model Performance Metrics', fontsize=16, fontweight='bold')
        
        # Risk score metrics
        if risk_metrics:
            names = list(risk_metrics.keys())
            values = list(risk_metrics.values())
            axes[0, 0].bar(range(len(names)), values, color='skyblue')
            axes[0, 0].set_title('Risk Score Metrics')
            axes[0, 0].set_xticks(range(len(names)))
            axes[0, 0].set_xticklabels(names, rotation=45, ha='right')
            axes[0, 0].grid(True, alpha=0.3)
        
        # Conclusion metrics
        if conclusion_metrics:
            names = list(conclusion_metrics.keys())
            values = list(conclusion_metrics.values())
            axes[0, 1].bar(range(len(names)), values, color='lightcoral')
            axes[0, 1].set_title('Conclusion Classification Metrics')
            axes[0, 1].set_xticks(range(len(names)))
            axes[0, 1].set_xticklabels(names, rotation=45, ha='right')
            axes[0, 1].grid(True, alpha=0.3)
        
        # Vessel-specific metrics
        if vessel_metrics:
            # Group by vessel
            vessels = ['LM', 'LAD', 'LCX', 'RCA']
            metric_types = set()
            for key in vessel_metrics.keys():
                for vessel in vessels:
                    if vessel in key:
                        metric_types.add(key.replace(f'{vessel}_', ''))
            
            if metric_types:
                x = np.arange(len(vessels))
                width = 0.8 / len(metric_types)
                
                for i, metric_type in enumerate(metric_types):
                    values = [vessel_metrics.get(f'{vessel}_{metric_type}', 0) for vessel in vessels]
                    axes[1, 0].bar(x + i * width, values, width, label=metric_type)
                
                axes[1, 0].set_title('Per-Vessel Metrics')
                axes[1, 0].set_xlabel('Vessel')
                axes[1, 0].set_xticks(x + width * (len(metric_types) - 1) / 2)
                axes[1, 0].set_xticklabels(vessels)
                axes[1, 0].legend()
                axes[1, 0].grid(True, alpha=0.3)
        
        # Overall summary
        summary_metrics = {k: v for k, v in metrics_dict.items() 
                          if not any(prefix in k for prefix in ['LM', 'LAD', 'LCX', 'RCA'])}
        if summary_metrics:
            names = list(summary_metrics.keys())[:10]  # Show top 10
            values = list(summary_metrics.values())[:10]
            axes[1, 1].barh(range(len(names)), values, color='lightgreen')
            axes[1, 1].set_title('Summary Metrics')
            axes[1, 1].set_yticks(range(len(names)))
            axes[1, 1].set_yticklabels(names)
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Metrics comparison saved to {save_path}")
        
        return fig
    
    def plot_prediction_distribution(self, predictions: Dict[str, torch.Tensor],
                                   targets: Dict[str, torch.Tensor],
                                   save_path: Optional[str] = None) -> plt.Figure:
        """
        Plot distribution of predictions vs targets.
        
        Args:
            predictions: Model predictions
            targets: Ground truth targets
            save_path: Path to save the plot
            
        Returns:
            Matplotlib figure
        """
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        fig.suptitle('Prediction vs Target Distributions', fontsize=16, fontweight='bold')
        
        # Risk scores for each vessel
        vessels = ['LM', 'LAD', 'LCX', 'RCA']
        risk_pred = predictions['risk_scores'].numpy()
        risk_target = targets['risk_scores'].numpy()
        
        for i, vessel in enumerate(vessels):
            if i < 4:  # First 4 subplots for vessels
                row, col = i // 2, i % 2
                
                # Handle NaN values
                valid_mask = ~np.isnan(risk_target[:, i])
                if valid_mask.any():
                    pred_vals = risk_pred[valid_mask, i]
                    target_vals = risk_target[valid_mask, i]
                    
                    axes[row, col].scatter(target_vals, pred_vals, alpha=0.6, s=20)
                    axes[row, col].plot([0, 1], [0, 1], 'r--', alpha=0.8)
                    axes[row, col].set_xlabel(f'{vessel} Target Risk Score')
                    axes[row, col].set_ylabel(f'{vessel} Predicted Risk Score')
                    axes[row, col].set_title(f'{vessel} Risk Score Prediction')
                    axes[row, col].grid(True, alpha=0.3)
                    
                    # Calculate correlation
                    corr = np.corrcoef(target_vals, pred_vals)[0, 1]
                    axes[row, col].text(0.05, 0.95, f'r = {corr:.3f}', 
                                       transform=axes[row, col].transAxes,
                                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # Conclusion prediction distribution
        conclusion_probs = predictions['conclusion_probs'].numpy().squeeze()
        conclusion_target = targets['conclusion'].numpy().squeeze()
        
        # Handle NaN values
        valid_mask = ~np.isnan(conclusion_target)
        if valid_mask.any():
            valid_probs = conclusion_probs[valid_mask]
            valid_target = conclusion_target[valid_mask]
            
            # Histogram by class
            pos_probs = valid_probs[valid_target == 1]
            neg_probs = valid_probs[valid_target == 0]
            
            axes[1, 2].hist(neg_probs, bins=30, alpha=0.7, label='Negative', color='blue')
            axes[1, 2].hist(pos_probs, bins=30, alpha=0.7, label='Positive', color='red')
            axes[1, 2].set_xlabel('Predicted Probability')
            axes[1, 2].set_ylabel('Frequency')
            axes[1, 2].set_title('Conclusion Prediction Distribution')
            axes[1, 2].legend()
            axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Prediction distribution plot saved to {save_path}")
        
        return fig
    
    def plot_mcg_sample(self, mcg_data: torch.Tensor, title: str = "MCG Sample",
                       save_path: Optional[str] = None) -> plt.Figure:
        """
        Plot MCG spatiotemporal data sample.
        
        Args:
            mcg_data: MCG data tensor of shape (seq_len, 1, 6, 6)
            title: Plot title
            save_path: Path to save the plot
            
        Returns:
            Matplotlib figure
        """
        # Convert to numpy and remove channel dimension
        if isinstance(mcg_data, torch.Tensor):
            mcg_data = mcg_data.numpy()
        
        if mcg_data.ndim == 4:
            mcg_data = mcg_data.squeeze(1)  # Remove channel dimension
        
        seq_len, height, width = mcg_data.shape
        
        fig, axes = plt.subplots(2, 3, figsize=(15, 8))
        fig.suptitle(title, fontsize=16, fontweight='bold')
        
        # Show spatial patterns at different time points
        time_points = [0, seq_len//4, seq_len//2, 3*seq_len//4, seq_len-1]
        
        for i, t in enumerate(time_points[:5]):
            row, col = i // 3, i % 3
            
            im = axes[row, col].imshow(mcg_data[t], cmap='RdBu_r', aspect='equal')
            axes[row, col].set_title(f'Time Step {t}')
            axes[row, col].set_xlabel('X Position')
            axes[row, col].set_ylabel('Y Position')
            plt.colorbar(im, ax=axes[row, col])
        
        # Show temporal evolution for center channel
        center_channel = mcg_data[:, height//2, width//2]
        axes[1, 2].plot(center_channel, linewidth=2)
        axes[1, 2].set_title('Center Channel Time Series')
        axes[1, 2].set_xlabel('Time Step')
        axes[1, 2].set_ylabel('Signal Amplitude')
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"MCG sample plot saved to {save_path}")
        
        return fig
    
    def create_evaluation_report(self, results: Dict[str, Any], 
                               save_dir: Optional[str] = None) -> Dict[str, plt.Figure]:
        """
        Create comprehensive evaluation report with multiple plots.
        
        Args:
            results: Evaluation results dictionary
            save_dir: Directory to save plots
            
        Returns:
            Dictionary of matplotlib figures
        """
        if save_dir is None:
            save_dir = self.save_dir
        
        figures = {}
        
        # Metrics comparison
        if 'metrics' in results:
            fig = self.plot_metrics_comparison(results['metrics'])
            figures['metrics_comparison'] = fig
            if save_dir:
                fig.savefig(os.path.join(save_dir, 'metrics_comparison.png'), 
                           dpi=300, bbox_inches='tight')
        
        # Confusion matrix
        if 'predictions' in results and 'targets' in results:
            conclusion_probs = results['predictions']['conclusion_probs'].numpy().squeeze()
            conclusion_target = results['targets']['conclusion'].numpy().squeeze()
            
            valid_mask = ~np.isnan(conclusion_target)
            if valid_mask.any():
                valid_probs = conclusion_probs[valid_mask]
                valid_target = conclusion_target[valid_mask]
                conclusion_pred = (valid_probs >= 0.5).astype(int)
                
                fig = self.plot_confusion_matrix(valid_target, conclusion_pred)
                figures['confusion_matrix'] = fig
                if save_dir:
                    fig.savefig(os.path.join(save_dir, 'confusion_matrix.png'), 
                               dpi=300, bbox_inches='tight')
        
        # ROC and PR curves
        if 'detailed_metrics' in results:
            fig_roc = self.plot_roc_curves(results['detailed_metrics'])
            figures['roc_curve'] = fig_roc
            if save_dir:
                fig_roc.savefig(os.path.join(save_dir, 'roc_curve.png'), 
                               dpi=300, bbox_inches='tight')
            
            fig_pr = self.plot_precision_recall_curve(results['detailed_metrics'])
            figures['pr_curve'] = fig_pr
            if save_dir:
                fig_pr.savefig(os.path.join(save_dir, 'pr_curve.png'), 
                              dpi=300, bbox_inches='tight')
        
        # Prediction distributions
        if 'predictions' in results and 'targets' in results:
            fig = self.plot_prediction_distribution(results['predictions'], results['targets'])
            figures['prediction_distribution'] = fig
            if save_dir:
                fig.savefig(os.path.join(save_dir, 'prediction_distribution.png'), 
                           dpi=300, bbox_inches='tight')
        
        logger.info(f"Evaluation report created with {len(figures)} plots")
        return figures