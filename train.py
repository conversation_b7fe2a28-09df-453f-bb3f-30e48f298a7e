#!/usr/bin/env python3
"""
Main training script for MCG Deep Learning Project
"""

import os
import sys
import argparse
import logging
import torch
import numpy as np
import random
from datetime import datetime

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.config import load_config
from src.data.dataset import MCGDataModule
from src.models.model_loader import create_model_from_config
from src.training.trainer import MCGTrainer
from src.evaluation.metrics import ModelEvaluator
from src.evaluation.visualization import MCGVisualizer
from src.utils.checkpoint import CheckpointManager


def setup_logging(log_level: str = "INFO") -> logging.Logger:
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('training.log')
        ]
    )
    return logging.getLogger(__name__)


def set_seed(seed: int = 42) -> None:
    """Set random seeds for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def get_device(config_device: str = "auto") -> torch.device:
    """Get appropriate device for training."""
    if config_device == "auto":
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    else:
        device = torch.device(config_device)
    
    if device.type == "cuda":
        print(f"Using GPU: {torch.cuda.get_device_name()}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    else:
        print("Using CPU")
    
    return device


def main():
    parser = argparse.ArgumentParser(description="Train MCG Deep Learning Model")
    parser.add_argument("--config", type=str, default="configs/config.yaml",
                       help="Path to configuration file")
    parser.add_argument("--resume", type=str, default=None,
                       help="Path to checkpoint to resume from")
    parser.add_argument("--seed", type=int, default=42,
                       help="Random seed for reproducibility")
    parser.add_argument("--log-level", type=str, default="INFO",
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="Logging level")
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging(args.log_level)
    logger.info("Starting MCG Deep Learning Training")
    logger.info(f"Arguments: {args}")
    
    # Set random seed
    set_seed(args.seed)
    logger.info(f"Random seed set to {args.seed}")
    
    try:
        # Load configuration
        logger.info(f"Loading configuration from {args.config}")
        config = load_config(args.config)
        
        # Get device
        device = get_device(config.hardware.device)
        
        # Create directories
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        run_dir = os.path.join("runs", f"mcg_training_{timestamp}")
        os.makedirs(run_dir, exist_ok=True)
        
        checkpoint_dir = os.path.join(run_dir, "checkpoints")
        tensorboard_dir = os.path.join(run_dir, "tensorboard")
        plots_dir = os.path.join(run_dir, "plots")
        
        for dir_path in [checkpoint_dir, tensorboard_dir, plots_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        logger.info(f"Run directory: {run_dir}")
        
        # Initialize data module
        logger.info("Setting up data module...")
        data_module = MCGDataModule(config)
        data_module.setup()
        
        # Get data information
        data_info = data_module.get_data_info()
        logger.info(f"Dataset info: {data_info}")
        
        # Detect data format by examining a sample batch
        logger.info("Detecting data format...")
        sample_batch = data_module.get_sample_batch()
        mcg_shape = sample_batch['mcg_data'].shape
        logger.info(f"MCG data shape: {mcg_shape}")
        
        # Determine if using 42 channels, 37 channels, or 6x6 spatial format
        use_42_channels = False
        use_37_channels = False
        
        if len(mcg_shape) == 3 and mcg_shape[2] == 42:
            use_42_channels = True
        elif len(mcg_shape) == 4 and mcg_shape[3] == 42:
            use_42_channels = True
        elif len(mcg_shape) == 3 and mcg_shape[2] == 37:
            use_37_channels = True
        elif len(mcg_shape) == 4 and mcg_shape[3] == 37:
            use_37_channels = True
        
        if use_42_channels:
            logger.info("Detected 42-channel format (36 signal + 6 timepoint channels)")
        elif use_37_channels:
            logger.info("Detected 37-channel format (36 MCG + 1 timepoint channel) - upgrading to 42-channel")
        else:
            logger.info("Detected 6x6 spatial format - upgrading to 42-channel format")
        
        # 统一使用42通道多模态模型
        logger.info("Creating 42-channel multimodal model...")
        model = create_model_from_config(
            config.model, 
            clinical_feature_dim=data_info['clinical_features_dim']
        )
        
        model = model.to(device)
        
        # Log model info
        model_info = model.get_model_info()
        logger.info(f"Model info: {model_info}")
        
        # Create trainer
        logger.info("Initializing trainer...")
        trainer = MCGTrainer(
            model=model,
            config=config,
            device=device,
            checkpoint_dir=checkpoint_dir,
            tensorboard_dir=tensorboard_dir
        )
        
        # Resume from checkpoint if specified
        start_epoch = 0
        if args.resume:
            logger.info(f"Resuming from checkpoint: {args.resume}")
            checkpoint_info = trainer.load_checkpoint(args.resume)
            start_epoch = checkpoint_info['epoch'] + 1
            logger.info(f"Resumed from epoch {start_epoch}")
        
        # Get data loaders
        train_loader = data_module.get_train_loader()
        test_loader = data_module.get_test_loader()
        
        logger.info(f"Training batches: {len(train_loader)}")
        logger.info(f"Validation batches: {len(test_loader)}")
        
        # Training
        logger.info("Starting training...")
        training_history = trainer.train(train_loader, test_loader)
        
        # Evaluation
        logger.info("Evaluating final model...")
        evaluator = ModelEvaluator(model, device, config.evaluation.metrics)
        
        # Evaluate on test set
        test_results = evaluator.evaluate(test_loader, return_predictions=True)
        logger.info(f"Test metrics: {test_results['metrics']}")
        
        # Create visualizations
        logger.info("Creating visualizations...")
        visualizer = MCGVisualizer(plots_dir)
        
        # Training curves
        if training_history:
            fig_training = visualizer.plot_training_curves(
                training_history, 
                save_path=os.path.join(plots_dir, "training_curves.png")
            )
        
        # Evaluation report
        evaluation_figures = visualizer.create_evaluation_report(
            test_results, save_dir=plots_dir
        )
        
        # Save final model
        logger.info("Saving final model...")
        checkpoint_manager = CheckpointManager(checkpoint_dir)
        
        # Save as standard checkpoint
        final_checkpoint = checkpoint_manager.save_checkpoint(
            model=model,
            optimizer=trainer.optimizer,
            epoch=trainer.current_epoch,
            metrics=test_results['metrics'],
            config=config,
            scheduler=trainer.scheduler,
            is_best=False,
            model_name="final_model"
        )
        
        # Export for deployment
        export_path = os.path.join(run_dir, "exported_model.pth")
        checkpoint_manager.export_model(
            model=model,
            export_path=export_path,
            format='pytorch',
            include_config=True,
            config=config
        )
        
        # Create model archive
        from src.utils.checkpoint import ModelSaver
        archive_path = os.path.join(run_dir, "model_archive.zip")
        ModelSaver.create_model_archive(
            model=model,
            config=config,
            metrics=test_results['metrics'],
            archive_path=archive_path,
            include_code=True
        )
        
        # Save results summary
        results_summary = {
            'training_completed': True,
            'final_epoch': trainer.current_epoch,
            'best_val_loss': trainer.best_val_loss,
            'test_metrics': test_results['metrics'],
            'model_info': model_info,
            'data_info': data_info,
            'config': config.__dict__,
            'run_directory': run_dir,
            'timestamp': timestamp
        }
        
        import json
        with open(os.path.join(run_dir, "results_summary.json"), 'w') as f:
            json.dump(results_summary, f, indent=2, default=str)
        
        logger.info(f"Training completed successfully!")
        logger.info(f"Results saved in: {run_dir}")
        logger.info(f"Best validation loss: {trainer.best_val_loss:.4f}")
        logger.info(f"Final test metrics: {test_results['metrics']}")
        
        return results_summary
        
    except Exception as e:
        logger.error(f"Training failed with error: {str(e)}")
        logger.exception("Full traceback:")
        raise


if __name__ == "__main__":
    main()