"""
终极42通道多模态Vision Transformer模型
整合所有历史优化策略：分离式处理 + 防过拟合 + 跨模态融合 + Transformer架构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any
import math
import logging

logger = logging.getLogger(__name__)


class PatchEmbedding1D(nn.Module):
    """1D卷积patch embedding - 适配不同长度序列"""
    
    def __init__(self, input_channels: int, embed_dim: int, patch_size: int = 16, 
                 max_seq_length: int = 2000):
        super().__init__()
        
        self.input_channels = input_channels
        self.embed_dim = embed_dim
        self.patch_size = patch_size
        self.max_seq_length = max_seq_length
        
        # 自适应patch数量计算
        self.num_patches = max_seq_length // patch_size
        
        # Patch投影
        self.patch_proj = nn.Conv1d(
            input_channels, embed_dim, 
            kernel_size=patch_size, stride=patch_size
        )
        
        # 位置编码 - 可学习
        self.position_embedding = nn.Parameter(
            torch.randn(1, self.num_patches, embed_dim) * 0.02
        )
        
        # 层归一化
        self.norm = nn.LayerNorm(embed_dim)
        
        logger.info(f"PatchEmbedding1D: {input_channels} channels, max {self.num_patches} patches, embed_dim={embed_dim}")
    
    def forward(self, x):
        # x: (batch_size, channels, seq_len)
        B, C, L = x.shape
        
        # 自适应处理不同长度的序列
        if L < self.patch_size:
            # 如果序列太短，进行padding
            pad_size = self.patch_size - L
            x = F.pad(x, (0, pad_size))
            L = self.patch_size
        
        # 计算实际patch数量
        actual_patches = L // self.patch_size
        
        # Patch投影
        x = self.patch_proj(x)  # (batch_size, embed_dim, num_patches)
        x = x.transpose(1, 2)  # (batch_size, num_patches, embed_dim)
        
        # 处理位置编码长度不匹配
        if actual_patches <= self.position_embedding.size(1):
            pos_embed = self.position_embedding[:, :actual_patches, :]
        else:
            # 如果实际patches更多，进行插值
            pos_embed = F.interpolate(
                self.position_embedding.transpose(1, 2), 
                size=actual_patches, 
                mode='linear'
            ).transpose(1, 2)
        
        # 添加位置编码
        x = x + pos_embed
        
        # 层归一化
        x = self.norm(x)
        
        return x


class MultiHeadSelfAttention(nn.Module):
    """多头自注意力 - 整合优化策略"""
    
    def __init__(self, embed_dim: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        
        assert embed_dim % num_heads == 0, "embed_dim must be divisible by num_heads"
        
        # 多头投影
        self.qkv_proj = nn.Linear(embed_dim, embed_dim * 3)
        self.out_proj = nn.Linear(embed_dim, embed_dim)
        
        # Dropout
        self.attn_dropout = nn.Dropout(dropout)
        self.proj_dropout = nn.Dropout(dropout)
        
        # 缩放因子
        self.scale = self.head_dim ** -0.5
    
    def forward(self, x):
        B, N, C = x.shape
        
        # 计算QKV
        qkv = self.qkv_proj(x).reshape(B, N, 3, self.num_heads, self.head_dim)
        qkv = qkv.permute(2, 0, 3, 1, 4)  # (3, B, num_heads, N, head_dim)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        # 注意力计算
        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = F.softmax(attn, dim=-1)
        attn = self.attn_dropout(attn)
        
        # 应用注意力
        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        
        # 输出投影
        x = self.out_proj(x)
        x = self.proj_dropout(x)
        
        return x


class TransformerBlock(nn.Module):
    """Transformer块 - 整合防过拟合策略"""
    
    def __init__(self, embed_dim: int, num_heads: int = 8, mlp_ratio: float = 4.0, 
                 dropout: float = 0.1, drop_path: float = 0.0):
        super().__init__()
        
        # 自注意力
        self.norm1 = nn.LayerNorm(embed_dim)
        self.attn = MultiHeadSelfAttention(embed_dim, num_heads, dropout)
        
        # MLP
        self.norm2 = nn.LayerNorm(embed_dim)
        mlp_hidden_dim = int(embed_dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(embed_dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(mlp_hidden_dim, embed_dim),
            nn.Dropout(dropout)
        )
        
        # Drop Path (随机深度) - 更强的正则化
        self.drop_path = DropPath(drop_path) if drop_path > 0.0 else nn.Identity()
    
    def forward(self, x):
        # 自注意力 + 残差连接
        x = x + self.drop_path(self.attn(self.norm1(x)))
        
        # MLP + 残差连接
        x = x + self.drop_path(self.mlp(self.norm2(x)))
        
        return x


class DropPath(nn.Module):
    """随机深度 - Drop Path正则化"""
    
    def __init__(self, drop_prob: float = 0.0):
        super().__init__()
        self.drop_prob = drop_prob
    
    def forward(self, x):
        if self.drop_prob == 0.0 or not self.training:
            return x
        
        keep_prob = 1 - self.drop_prob
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)
        random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
        random_tensor.floor_()  # 二值化
        output = x.div(keep_prob) * random_tensor
        
        return output


class SignalViTProcessor(nn.Module):
    """信号ViT处理器 - 专门处理36个信号通道"""
    
    def __init__(self, input_channels: int = 36, embed_dim: int = 768, 
                 num_layers: int = 12, num_heads: int = 12, 
                 patch_size: int = 16, dropout: float = 0.1, drop_path_rate: float = 0.1):
        super().__init__()
        
        self.embed_dim = embed_dim
        
        # Patch Embedding
        self.patch_embed = PatchEmbedding1D(
            input_channels=input_channels,
            embed_dim=embed_dim,
            patch_size=patch_size
        )
        
        # Transformer层
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, num_layers)]
        self.transformer_layers = nn.ModuleList([
            TransformerBlock(
                embed_dim=embed_dim,
                num_heads=num_heads,
                mlp_ratio=4.0,
                dropout=dropout,
                drop_path=dpr[i]
            ) for i in range(num_layers)
        ])
        
        # 最终层归一化
        self.norm = nn.LayerNorm(embed_dim)
        
        # CLS token (用于全局表示)
        self.cls_token = nn.Parameter(torch.randn(1, 1, embed_dim) * 0.02)
        
        logger.info(f"SignalViTProcessor: {input_channels} → {embed_dim}D, {num_layers} layers, {num_heads} heads")
    
    def forward(self, x):
        B = x.shape[0]
        
        # Patch embedding
        x = self.patch_embed(x)  # (B, num_patches, embed_dim)
        
        # 添加CLS token
        cls_tokens = self.cls_token.expand(B, -1, -1)
        x = torch.cat([cls_tokens, x], dim=1)  # (B, num_patches + 1, embed_dim)
        
        # Transformer层
        for layer in self.transformer_layers:
            x = layer(x)
        
        # 最终归一化
        x = self.norm(x)
        
        # 返回CLS token的表示
        cls_representation = x[:, 0]  # (B, embed_dim)
        
        return cls_representation


class TimepointViTProcessor(nn.Module):
    """时刻点ViT处理器 - 轻量级Transformer"""
    
    def __init__(self, timepoint_channels: int = 6, embed_dim: int = 384, 
                 num_layers: int = 6, num_heads: int = 6, 
                 patch_size: int = 32, dropout: float = 0.1):
        super().__init__()
        
        self.embed_dim = embed_dim
        
        # Patch Embedding
        self.patch_embed = PatchEmbedding1D(
            input_channels=timepoint_channels,
            embed_dim=embed_dim,
            patch_size=patch_size,
            max_seq_length=1000  # 时刻点序列通常较短
        )
        
        # Transformer层 (较少层数)
        self.transformer_layers = nn.ModuleList([
            TransformerBlock(
                embed_dim=embed_dim,
                num_heads=num_heads,
                mlp_ratio=4.0,
                dropout=dropout,
                drop_path=0.1
            ) for _ in range(num_layers)
        ])
        
        # 最终层归一化
        self.norm = nn.LayerNorm(embed_dim)
        
        # CLS token
        self.cls_token = nn.Parameter(torch.randn(1, 1, embed_dim) * 0.02)
        
        logger.info(f"TimepointViTProcessor: {timepoint_channels} → {embed_dim}D, {num_layers} layers")
    
    def forward(self, x):
        B = x.shape[0]
        
        # Patch embedding
        x = self.patch_embed(x)
        
        # 添加CLS token
        cls_tokens = self.cls_token.expand(B, -1, -1)
        x = torch.cat([cls_tokens, x], dim=1)
        
        # Transformer层
        for layer in self.transformer_layers:
            x = layer(x)
        
        # 最终归一化并返回CLS token
        x = self.norm(x)
        cls_representation = x[:, 0]
        
        return cls_representation


class CrossModalTransformerFusion(nn.Module):
    """跨模态Transformer融合 - 高级融合策略"""
    
    def __init__(self, signal_dim: int, timepoint_dim: int, embed_dim: int = 512, 
                 num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        
        self.embed_dim = embed_dim
        
        # 投影到统一空间
        self.signal_proj = nn.Linear(signal_dim, embed_dim)
        self.timepoint_proj = nn.Linear(timepoint_dim, embed_dim)
        
        # 跨模态注意力
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # 融合Transformer
        self.fusion_transformer = TransformerBlock(
            embed_dim=embed_dim * 2,  # 拼接后的维度
            num_heads=num_heads,
            dropout=dropout
        )
        
        # 输出投影
        self.output_proj = nn.Sequential(
            nn.LayerNorm(embed_dim * 2),
            nn.Linear(embed_dim * 2, embed_dim),
            nn.GELU(),
            nn.Dropout(dropout)
        )
        
        self.output_dim = embed_dim
        logger.info(f"CrossModalTransformerFusion: signal={signal_dim}D + timepoint={timepoint_dim}D → {embed_dim}D")
    
    def forward(self, signal_features, timepoint_features):
        # 投影到统一空间
        signal_proj = self.signal_proj(signal_features).unsqueeze(1)  # (B, 1, embed_dim)
        timepoint_proj = self.timepoint_proj(timepoint_features).unsqueeze(1)  # (B, 1, embed_dim)
        
        # 跨模态注意力
        signal_attended, _ = self.cross_attention(signal_proj, timepoint_proj, timepoint_proj)
        timepoint_attended, _ = self.cross_attention(timepoint_proj, signal_proj, signal_proj)
        
        # 拼接
        fused = torch.cat([signal_attended.squeeze(1), timepoint_attended.squeeze(1)], dim=1)  # (B, embed_dim * 2)
        
        # Transformer融合
        fused = fused.unsqueeze(1)  # (B, 1, embed_dim * 2)
        fused = self.fusion_transformer(fused)
        fused = fused.squeeze(1)  # (B, embed_dim * 2)
        
        # 输出投影
        output = self.output_proj(fused)
        
        return output


class MultiModal42ChannelViT(nn.Module):
    """终极42通道多模态Vision Transformer模型"""
    
    def __init__(self, config: Dict[str, Any], clinical_feature_dim: int = 0):
        super().__init__()
        
        self.config = config
        self.clinical_feature_dim = clinical_feature_dim
        
        # 信号ViT配置
        signal_config = config.get('signal_vit', {})
        self.signal_processor = SignalViTProcessor(
            input_channels=36,
            embed_dim=signal_config.get('embed_dim', 768),
            num_layers=signal_config.get('num_layers', 12),
            num_heads=signal_config.get('num_heads', 12),
            patch_size=signal_config.get('patch_size', 16),
            dropout=signal_config.get('dropout', 0.1),
            drop_path_rate=signal_config.get('drop_path_rate', 0.1)
        )
        
        # 时刻点ViT配置
        timepoint_config = config.get('timepoint_vit', {})
        self.timepoint_processor = TimepointViTProcessor(
            timepoint_channels=6,
            embed_dim=timepoint_config.get('embed_dim', 384),
            num_layers=timepoint_config.get('num_layers', 6),
            num_heads=timepoint_config.get('num_heads', 6),
            patch_size=timepoint_config.get('patch_size', 32),
            dropout=timepoint_config.get('dropout', 0.1)
        )
        
        # 跨模态Transformer融合
        fusion_config = config.get('fusion', {})
        self.cross_modal_fusion = CrossModalTransformerFusion(
            signal_dim=self.signal_processor.embed_dim,
            timepoint_dim=self.timepoint_processor.embed_dim,
            embed_dim=fusion_config.get('embed_dim', 512),
            num_heads=fusion_config.get('num_heads', 8),
            dropout=fusion_config.get('dropout', 0.1)
        )
        
        # 临床特征处理
        clinical_hidden = config.get('clinical', {}).get('hidden_dim', 256)
        if clinical_feature_dim > 0:
            self.clinical_processor = nn.Sequential(
                nn.Linear(clinical_feature_dim, clinical_hidden),
                nn.LayerNorm(clinical_hidden),
                nn.GELU(),
                nn.Dropout(config.get('clinical', {}).get('dropout', 0.3)),
                nn.Linear(clinical_hidden, clinical_hidden),
                nn.LayerNorm(clinical_hidden)
            )
        
        # 最终特征融合
        fusion_input_dim = (self.cross_modal_fusion.output_dim + 
                           (clinical_hidden if clinical_feature_dim > 0 else 0))
        
        final_hidden = config.get('final_fusion', {}).get('hidden_dim', 1024)
        self.final_fusion = nn.Sequential(
            nn.Linear(fusion_input_dim, final_hidden),
            nn.LayerNorm(final_hidden),
            nn.GELU(),
            nn.Dropout(config.get('final_fusion', {}).get('dropout', 0.3)),
            nn.Linear(final_hidden, final_hidden),
            nn.LayerNorm(final_hidden),
            nn.GELU()
        )
        
        # 多任务预测头
        output_config = config.get('output', {})
        
        # 风险评分头
        risk_hidden = output_config.get('risk_head_hidden', 512)
        self.risk_head = nn.Sequential(
            nn.Linear(final_hidden, risk_hidden),
            nn.LayerNorm(risk_hidden),
            nn.GELU(),
            nn.Dropout(output_config.get('dropout', 0.4)),
            nn.Linear(risk_hidden, risk_hidden // 2),
            nn.LayerNorm(risk_hidden // 2),
            nn.GELU(),
            nn.Dropout(output_config.get('dropout', 0.4)),
            nn.Linear(risk_hidden // 2, 4),
            nn.Sigmoid()
        )
        
        # 结论预测头
        conclusion_hidden = output_config.get('conclusion_head_hidden', 256)
        self.conclusion_head = nn.Sequential(
            nn.Linear(final_hidden, conclusion_hidden),
            nn.LayerNorm(conclusion_hidden),
            nn.GELU(),
            nn.Dropout(output_config.get('dropout', 0.4)),
            nn.Linear(conclusion_hidden, conclusion_hidden // 2),
            nn.LayerNorm(conclusion_hidden // 2),
            nn.GELU(),
            nn.Dropout(output_config.get('dropout', 0.4)),
            nn.Linear(conclusion_hidden // 2, 1)
        )
        
        # 权重初始化
        self.apply(self._init_weights)
        
        # 模型信息
        total_params = sum(p.numel() for p in self.parameters())
        logger.info(f"MultiModal42ChannelViT initialized: {total_params:,} parameters")
    
    def _init_weights(self, module):
        """权重初始化 - ViT特化"""
        if isinstance(module, nn.Linear):
            torch.nn.init.trunc_normal_(module.weight, std=0.02)
            if module.bias is not None:
                nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            nn.init.ones_(module.weight)
            nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Conv1d):
            torch.nn.init.trunc_normal_(module.weight, std=0.02)
    
    def forward(self, multimodal_data: torch.Tensor, clinical_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        # 转换格式
        multimodal_data = multimodal_data.transpose(1, 2)  # (batch, 42, seq_len)
        
        # 分离通道
        signal_data = multimodal_data[:, :36, :]  # 前36通道
        timepoint_data = multimodal_data[:, 36:, :]  # 后6通道
        
        # ViT处理
        signal_features = self.signal_processor(signal_data)
        timepoint_features = self.timepoint_processor(timepoint_data)
        
        # 跨模态Transformer融合
        fused_features = self.cross_modal_fusion(signal_features, timepoint_features)
        
        # 临床特征融合
        if self.clinical_feature_dim > 0 and clinical_features.numel() > 0:
            clinical_features = self.clinical_processor(clinical_features)
            fused_features = torch.cat([fused_features, clinical_features], dim=1)
        
        # 最终融合
        final_features = self.final_fusion(fused_features)
        
        # 多任务预测
        risk_scores = self.risk_head(final_features)
        conclusion_logits = self.conclusion_head(final_features)
        
        return {
            'risk_scores': risk_scores,
            'conclusion_logits': conclusion_logits,
            'fused_features': final_features,
            'signal_features': signal_features,
            'timepoint_features': timepoint_features
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        total_params = sum(p.numel() for p in self.parameters())
        return {
            'architecture': 'MultiModal42ChannelViT',
            'input_channels': 42,
            'signal_channels': 36,
            'timepoint_channels': 6,
            'total_parameters': total_params,
            'clinical_feature_dim': self.clinical_feature_dim,
            'optimization_features': [
                'vision_transformer_architecture',
                'cross_modal_transformer_fusion',
                'dual_branch_processing',
                'drop_path_regularization',
                'adaptive_patch_embedding',
                'cls_token_representation'
            ]
        }


def create_multimodal_vit_model(config: Dict[str, Any], clinical_feature_dim: int = 0) -> MultiModal42ChannelViT:
    """创建42通道多模态ViT模型"""
    return MultiModal42ChannelViT(config, clinical_feature_dim)