import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from sklearn.metrics import roc_curve, precision_recall_curve, confusion_matrix
import logging
import os

logger = logging.getLogger(__name__)

# 设置图表样式
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


class MedicalVisualizer:
    """医学专用可视化工具"""
    
    def __init__(self, save_dir: str = "medical_plots"):
        """
        初始化医学可视化工具
        
        Args:
            save_dir: 图表保存目录
        """
        self.save_dir = save_dir
        self.vessel_names = ['LM', 'LAD', 'LCX', 'RCA']
        self.vessel_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A']
        
        os.makedirs(save_dir, exist_ok=True)
        
    def plot_medical_metrics_dashboard(self, medical_report: Dict[str, Any], 
                                     save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制医学指标仪表板
        
        Args:
            medical_report: 医学评估报告
            save_path: 保存路径
            
        Returns:
            matplotlib Figure对象
        """
        fig = plt.figure(figsize=(20, 16))
        gs = fig.add_gridspec(4, 4, height_ratios=[1, 1, 1, 1], width_ratios=[1, 1, 1, 1])
        
        # 1. 造影结论指标摘要
        ax1 = fig.add_subplot(gs[0, :2])
        self._plot_conclusion_summary(ax1, medical_report.get('conclusion_metrics', {}))
        
        # 2. 血管风险评分总览
        ax2 = fig.add_subplot(gs[0, 2:])
        self._plot_vessel_overview(ax2, medical_report.get('vessel_metrics', {}))
        
        # 3. ROC曲线对比
        ax3 = fig.add_subplot(gs[1, :2])
        self._plot_roc_comparison(ax3, medical_report)
        
        # 4. 相关性热图
        ax4 = fig.add_subplot(gs[1, 2:])
        self._plot_correlation_heatmap(ax4, medical_report.get('vessel_metrics', {}))
        
        # 5. 血管特异性分析
        ax5 = fig.add_subplot(gs[2, :])
        self._plot_vessel_specific_analysis(ax5, medical_report.get('vessel_metrics', {}))
        
        # 6. 性能评级雷达图
        ax6 = fig.add_subplot(gs[3, :2])
        self._plot_performance_radar(ax6, medical_report)
        
        # 7. 临床决策阈值分析
        ax7 = fig.add_subplot(gs[3, 2:])
        self._plot_threshold_analysis(ax7, medical_report.get('vessel_metrics', {}))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"医学指标仪表板已保存到: {save_path}")
        
        return fig
    
    def plot_bland_altman(self, y_true: np.ndarray, y_pred: np.ndarray, 
                         title: str = "Bland-Altman Plot", 
                         save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制Bland-Altman一致性图
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            title: 图表标题
            save_path: 保存路径
            
        Returns:
            matplotlib Figure对象
        """
        # 处理NaN值
        valid_mask = ~(np.isnan(y_true) | np.isnan(y_pred))
        if not valid_mask.any():
            logger.warning("没有有效数据绘制Bland-Altman图")
            return plt.figure()
        
        y_true_clean = y_true[valid_mask]
        y_pred_clean = y_pred[valid_mask]
        
        # 计算差值和平均值
        diff = y_pred_clean - y_true_clean
        mean_val = (y_pred_clean + y_true_clean) / 2
        
        # 统计量
        mean_diff = np.mean(diff)
        std_diff = np.std(diff)
        loa_upper = mean_diff + 1.96 * std_diff
        loa_lower = mean_diff - 1.96 * std_diff
        
        # 绘制图形
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # 散点图
        ax.scatter(mean_val, diff, alpha=0.6, s=30)
        
        # 平均差值线
        ax.axhline(mean_diff, color='red', linestyle='-', linewidth=2, 
                  label=f'Mean Diff: {mean_diff:.3f}')
        
        # 一致性界限
        ax.axhline(loa_upper, color='red', linestyle='--', linewidth=1, 
                  label=f'Upper LoA: {loa_upper:.3f}')
        ax.axhline(loa_lower, color='red', linestyle='--', linewidth=1, 
                  label=f'Lower LoA: {loa_lower:.3f}')
        
        # 零线
        ax.axhline(0, color='black', linestyle='-', alpha=0.3)
        
        # 设置标签和标题
        ax.set_xlabel('Mean of True and Predicted Values', fontsize=12)
        ax.set_ylabel('Difference (Predicted - True)', fontsize=12)
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        stats_text = f'n = {len(y_true_clean)}\n'
        stats_text += f'Mean ± SD: {mean_diff:.3f} ± {std_diff:.3f}\n'
        stats_text += f'95% LoA: [{loa_lower:.3f}, {loa_upper:.3f}]'
        
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Bland-Altman图已保存到: {save_path}")
        
        return fig
    
    def plot_forest_plot(self, metrics_data: Dict[str, Dict[str, float]], 
                        metric_name: str = 'auc_roc',
                        save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制森林图显示不同组的置信区间
        
        Args:
            metrics_data: 指标数据字典
            metric_name: 要显示的指标名称
            save_path: 保存路径
            
        Returns:
            matplotlib Figure对象
        """
        # 准备数据
        groups = []
        values = []
        ci_lowers = []
        ci_uppers = []
        
        for group_name, metrics in metrics_data.items():
            if metric_name in metrics:
                groups.append(group_name)
                values.append(metrics[metric_name])
                
                # 尝试获取置信区间
                ci_lower_key = f"{metric_name.replace('_', '_ci_lower_')}" if 'auc' in metric_name else f"{metric_name}_ci_lower"
                ci_upper_key = f"{metric_name.replace('_', '_ci_upper_')}" if 'auc' in metric_name else f"{metric_name}_ci_upper"
                
                ci_lower = metrics.get('auc_ci_lower', metrics[metric_name] - 0.05) if 'auc' in metric_name else metrics.get(ci_lower_key, metrics[metric_name] - 0.05)
                ci_upper = metrics.get('auc_ci_upper', metrics[metric_name] + 0.05) if 'auc' in metric_name else metrics.get(ci_upper_key, metrics[metric_name] + 0.05)
                
                ci_lowers.append(ci_lower)
                ci_uppers.append(ci_upper)
        
        if not groups:
            logger.warning(f"没有找到指标 {metric_name} 的数据")
            return plt.figure()
        
        # 绘制森林图
        fig, ax = plt.subplots(figsize=(10, max(6, len(groups) * 0.8)))
        
        y_pos = np.arange(len(groups))
        
        # 绘制点和置信区间
        for i, (group, value, ci_low, ci_high) in enumerate(zip(groups, values, ci_lowers, ci_uppers)):
            # 置信区间线
            ax.plot([ci_low, ci_high], [i, i], 'k-', linewidth=2)
            
            # 置信区间端点
            ax.plot([ci_low, ci_low], [i-0.1, i+0.1], 'k-', linewidth=2)
            ax.plot([ci_high, ci_high], [i-0.1, i+0.1], 'k-', linewidth=2)
            
            # 中心点
            ax.plot(value, i, 'ro', markersize=8)
            
            # 数值标签
            ax.text(ci_high + 0.01, i, f'{value:.3f} [{ci_low:.3f}, {ci_high:.3f}]', 
                   verticalalignment='center', fontsize=10)
        
        # 设置坐标轴
        ax.set_yticks(y_pos)
        ax.set_yticklabels(groups)
        ax.set_xlabel(f'{metric_name.replace("_", " ").title()} (95% CI)', fontsize=12)
        ax.set_title(f'Forest Plot - {metric_name.replace("_", " ").title()}', fontsize=14, fontweight='bold')
        
        # 添加参考线
        if 'auc' in metric_name.lower():
            ax.axvline(x=0.5, color='gray', linestyle='--', alpha=0.5, label='No discrimination')
        elif 'accuracy' in metric_name.lower() or 'precision' in metric_name.lower():
            ax.axvline(x=0.5, color='gray', linestyle='--', alpha=0.5)
        
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"森林图已保存到: {save_path}")
        
        return fig
    
    def plot_calibration_plot(self, y_true: np.ndarray, y_prob: np.ndarray, 
                            n_bins: int = 10, title: str = "Calibration Plot",
                            save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制校准图评估概率预测的准确性
        
        Args:
            y_true: 真实标签 (0/1)
            y_prob: 预测概率 (0-1)
            n_bins: 分箱数量
            title: 图表标题
            save_path: 保存路径
            
        Returns:
            matplotlib Figure对象
        """
        # 处理NaN值
        valid_mask = ~(np.isnan(y_true) | np.isnan(y_prob))
        if not valid_mask.any():
            logger.warning("没有有效数据绘制校准图")
            return plt.figure()
        
        y_true_clean = y_true[valid_mask].astype(int)
        y_prob_clean = y_prob[valid_mask]
        
        # 分箱
        bin_boundaries = np.linspace(0, 1, n_bins + 1)
        bin_lowers = bin_boundaries[:-1]
        bin_uppers = bin_boundaries[1:]
        
        # 计算每个箱的实际阳性率和预测概率
        bin_centers = []
        bin_true_rates = []
        bin_counts = []
        
        for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):
            in_bin = (y_prob_clean > bin_lower) & (y_prob_clean <= bin_upper)
            if in_bin.sum() > 0:
                bin_centers.append((bin_lower + bin_upper) / 2)
                bin_true_rates.append(y_true_clean[in_bin].mean())
                bin_counts.append(in_bin.sum())
        
        # 绘制校准图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 主校准图
        if bin_centers:
            ax1.plot(bin_centers, bin_true_rates, 'o-', linewidth=2, markersize=8, label='Model')
        ax1.plot([0, 1], [0, 1], 'k--', alpha=0.7, label='Perfect Calibration')
        
        ax1.set_xlabel('Mean Predicted Probability', fontsize=12)
        ax1.set_ylabel('Fraction of Positives', fontsize=12)
        ax1.set_title(title, fontsize=14, fontweight='bold')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_xlim([0, 1])
        ax1.set_ylim([0, 1])
        
        # 添加样本数信息
        for center, count in zip(bin_centers, bin_counts):
            ax1.text(center, -0.05, str(count), ha='center', fontsize=8)
        
        # 分布直方图
        ax2.hist(y_prob_clean[y_true_clean == 0], bins=20, alpha=0.7, label='Negative', 
                density=True, color='blue')
        ax2.hist(y_prob_clean[y_true_clean == 1], bins=20, alpha=0.7, label='Positive', 
                density=True, color='red')
        
        ax2.set_xlabel('Predicted Probability', fontsize=12)
        ax2.set_ylabel('Density', fontsize=12)
        ax2.set_title('Prediction Distribution', fontsize=14, fontweight='bold')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"校准图已保存到: {save_path}")
        
        return fig
    
    def _plot_conclusion_summary(self, ax: plt.Axes, conclusion_metrics: Dict[str, float]):
        """绘制造影结论指标摘要"""
        if not conclusion_metrics:
            ax.text(0.5, 0.5, 'No conclusion metrics available', 
                   ha='center', va='center', transform=ax.transAxes)
            ax.set_title('Conclusion Metrics Summary')
            return
        
        # 主要指标
        main_metrics = ['accuracy', 'sensitivity', 'specificity', 'auc_roc']
        values = [conclusion_metrics.get(metric, 0) for metric in main_metrics]
        labels = ['Accuracy', 'Sensitivity', 'Specificity', 'AUC-ROC']
        
        # 柱状图
        bars = ax.bar(labels, values, color=['skyblue', 'lightgreen', 'lightcoral', 'gold'])
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        ax.set_ylim(0, 1.1)
        ax.set_title('Conclusion Classification Metrics', fontsize=12, fontweight='bold')
        ax.grid(True, alpha=0.3)
        
        # 添加样本信息
        sample_size = conclusion_metrics.get('sample_size', 0)
        positive_cases = conclusion_metrics.get('positive_cases', 0)
        ax.text(0.02, 0.98, f'n={sample_size}, positive={positive_cases}', 
               transform=ax.transAxes, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    def _plot_vessel_overview(self, ax: plt.Axes, vessel_metrics: Dict[str, Dict[str, float]]):
        """绘制血管风险评分总览"""
        if not vessel_metrics:
            ax.text(0.5, 0.5, 'No vessel metrics available', 
                   ha='center', va='center', transform=ax.transAxes)
            ax.set_title('Vessel Risk Score Overview')
            return
        
        vessels = [v for v in self.vessel_names if v in vessel_metrics]
        if not vessels:
            ax.text(0.5, 0.5, 'No valid vessel data', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # 获取R²值
        r_squared_values = [vessel_metrics[vessel].get('r_squared', 0) for vessel in vessels]
        mae_values = [vessel_metrics[vessel].get('mae', 0) for vessel in vessels]
        
        # 双轴图
        ax2 = ax.twinx()
        
        # R²柱状图 (左轴)
        bars1 = ax.bar(vessels, r_squared_values, alpha=0.7, color=self.vessel_colors[:len(vessels)], 
                      label='R²')
        
        # MAE折线图 (右轴)
        line = ax2.plot(vessels, mae_values, 'ro-', linewidth=2, markersize=8, label='MAE')
        
        # 设置标签
        ax.set_ylabel('R² Score', fontsize=12)
        ax2.set_ylabel('Mean Absolute Error', fontsize=12)
        ax.set_title('Vessel Prediction Performance', fontsize=12, fontweight='bold')
        
        # 添加数值标签
        for bar, r2 in zip(bars1, r_squared_values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{r2:.3f}', ha='center', va='bottom', fontsize=10)
        
        for i, (vessel, mae) in enumerate(zip(vessels, mae_values)):
            ax2.text(i, mae + 0.005, f'{mae:.3f}', ha='center', va='bottom', fontsize=10)
        
        ax.set_ylim(0, 1.1)
        ax.grid(True, alpha=0.3)
        
        # 图例
        lines1, labels1 = ax.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax.legend(lines1 + lines2, labels1 + labels2, loc='upper right')
    
    def _plot_roc_comparison(self, ax: plt.Axes, medical_report: Dict[str, Any]):
        """绘制ROC曲线对比"""
        ax.plot([0, 1], [0, 1], 'k--', alpha=0.5, label='Random')
        
        # 造影结论ROC
        conclusion_auc = medical_report.get('conclusion_metrics', {}).get('auc_roc', 0)
        ax.plot([0, 0.2, 1], [0, 0.8, 1], label=f'Conclusion (AUC={conclusion_auc:.3f})')
        
        ax.set_xlabel('False Positive Rate')
        ax.set_ylabel('True Positive Rate')
        ax.set_title('ROC Curves Comparison')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_correlation_heatmap(self, ax: plt.Axes, vessel_metrics: Dict[str, Dict[str, float]]):
        """绘制相关性热图"""
        vessels = [v for v in self.vessel_names if v in vessel_metrics]
        if len(vessels) < 2:
            ax.text(0.5, 0.5, 'Insufficient data for correlation', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # 创建相关性矩阵 (使用R²作为示例)
        r_squared_matrix = np.eye(len(vessels))
        for i, vessel in enumerate(vessels):
            r_squared_matrix[i, i] = vessel_metrics[vessel].get('r_squared', 0)
        
        # 热图
        sns.heatmap(r_squared_matrix, annot=True, cmap='Blues', 
                   xticklabels=vessels, yticklabels=vessels, ax=ax,
                   vmin=0, vmax=1, square=True)
        ax.set_title('Vessel Performance Heatmap (R²)')
    
    def _plot_vessel_specific_analysis(self, ax: plt.Axes, vessel_metrics: Dict[str, Dict[str, float]]):
        """绘制血管特异性分析"""
        vessels = [v for v in self.vessel_names if v in vessel_metrics]
        if not vessels:
            ax.text(0.5, 0.5, 'No vessel data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # 不同狭窄程度的MAE
        stenosis_levels = ['mild', 'moderate', 'severe']
        width = 0.25
        x = np.arange(len(vessels))
        
        for i, level in enumerate(stenosis_levels):
            mae_values = []
            for vessel in vessels:
                mae_key = f'{level}_stenosis_mae'
                mae_val = vessel_metrics[vessel].get(mae_key, 0)
                mae_values.append(mae_val)
            
            ax.bar(x + i*width, mae_values, width, label=f'{level.title()} Stenosis', 
                  alpha=0.8)
        
        ax.set_xlabel('Vessels')
        ax.set_ylabel('Mean Absolute Error')
        ax.set_title('Vessel-Specific Analysis by Stenosis Severity')
        ax.set_xticks(x + width)
        ax.set_xticklabels(vessels)
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_performance_radar(self, ax: plt.Axes, medical_report: Dict[str, Any]):
        """绘制性能评级雷达图"""
        # 性能指标
        performance_summary = medical_report.get('performance_summary', {})
        
        # 简化的雷达图显示
        categories = ['Overall', 'Conclusion', 'Vessels']
        
        # 转换性能等级为数值
        perf_map = {'Excellent': 4, 'Good': 3, 'Fair': 2, 'Poor': 1}
        
        values = [
            perf_map.get(performance_summary.get('overall_performance'), 1),
            perf_map.get(performance_summary.get('conclusion_performance'), 1),
            perf_map.get(performance_summary.get('vessel_performance'), 1)
        ]
        
        # 柱状图替代雷达图 (简化)
        bars = ax.bar(categories, values, color=['gold', 'lightblue', 'lightgreen'])
        
        # 添加等级标签
        performance_labels = {4: 'Excellent', 3: 'Good', 2: 'Fair', 1: 'Poor'}
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                   performance_labels[value], ha='center', va='bottom', fontweight='bold')
        
        ax.set_ylim(0, 5)
        ax.set_ylabel('Performance Level')
        ax.set_title('Performance Rating Summary')
        ax.grid(True, alpha=0.3)
    
    def _plot_threshold_analysis(self, ax: plt.Axes, vessel_metrics: Dict[str, Dict[str, float]]):
        """绘制临床决策阈值分析"""
        vessels = [v for v in self.vessel_names if v in vessel_metrics]
        if not vessels:
            ax.text(0.5, 0.5, 'No threshold data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # 50%和70%阈值的准确率
        thresholds = [50, 70]
        width = 0.35
        x = np.arange(len(vessels))
        
        for i, threshold in enumerate(thresholds):
            acc_values = []
            for vessel in vessels:
                acc_key = f'threshold_{threshold}_accuracy'
                acc_val = vessel_metrics[vessel].get(acc_key, 0)
                acc_values.append(acc_val)
            
            ax.bar(x + i*width, acc_values, width, 
                  label=f'{threshold}% Threshold', alpha=0.8)
        
        ax.set_xlabel('Vessels')
        ax.set_ylabel('Accuracy')
        ax.set_title('Clinical Decision Threshold Analysis')
        ax.set_xticks(x + width/2)
        ax.set_xticklabels(vessels)
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 1.1)
    
    def create_comprehensive_medical_report(self, medical_report: Dict[str, Any], 
                                          predictions: Dict[str, Any], 
                                          targets: Dict[str, Any],
                                          save_dir: Optional[str] = None) -> Dict[str, plt.Figure]:
        """
        创建全面的医学可视化报告
        
        Args:
            medical_report: 医学评估报告
            predictions: 预测结果
            targets: 真实标签
            save_dir: 保存目录
            
        Returns:
            图表字典
        """
        if save_dir is None:
            save_dir = self.save_dir
        
        figures = {}
        
        # 1. 主仪表板
        fig_dashboard = self.plot_medical_metrics_dashboard(medical_report)
        figures['dashboard'] = fig_dashboard
        if save_dir:
            fig_dashboard.savefig(os.path.join(save_dir, 'medical_dashboard.png'), 
                                dpi=300, bbox_inches='tight')
        
        # 2. 血管Bland-Altman图
        if 'risk_scores' in predictions and 'risk_scores' in targets:
            risk_pred = predictions['risk_scores'].numpy()
            risk_target = targets['risk_scores'].numpy()
            
            for i, vessel in enumerate(self.vessel_names):
                if i < risk_pred.shape[1]:
                    fig_ba = self.plot_bland_altman(
                        risk_target[:, i], risk_pred[:, i],
                        title=f'{vessel} Vessel - Bland-Altman Plot'
                    )
                    figures[f'bland_altman_{vessel}'] = fig_ba
                    if save_dir:
                        fig_ba.savefig(os.path.join(save_dir, f'bland_altman_{vessel}.png'), 
                                     dpi=300, bbox_inches='tight')
        
        # 3. 森林图
        if 'vessel_metrics' in medical_report:
            fig_forest = self.plot_forest_plot(
                medical_report['vessel_metrics'], 
                metric_name='r_squared'
            )
            figures['forest_plot'] = fig_forest
            if save_dir:
                fig_forest.savefig(os.path.join(save_dir, 'forest_plot.png'), 
                                 dpi=300, bbox_inches='tight')
        
        # 4. 校准图
        if 'conclusion_probs' in predictions and 'conclusion' in targets:
            conclusion_probs = predictions['conclusion_probs'].numpy().squeeze()
            conclusion_target = targets['conclusion'].numpy().squeeze()
            
            fig_calib = self.plot_calibration_plot(
                conclusion_target, conclusion_probs,
                title='Conclusion Prediction Calibration'
            )
            figures['calibration_plot'] = fig_calib
            if save_dir:
                fig_calib.savefig(os.path.join(save_dir, 'calibration_plot.png'), 
                                dpi=300, bbox_inches='tight')
        
        logger.info(f"医学可视化报告已创建，包含 {len(figures)} 个图表")
        return figures