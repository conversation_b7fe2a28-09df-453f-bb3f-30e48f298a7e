import os
import pandas as pd
import numpy as np
from typing import Tu<PERSON>, Dict, Optional, List
from sklearn.preprocessing import LabelEncoder, StandardScaler, MaxAbsScaler, MinMaxScaler
import logging

logger = logging.getLogger(__name__)


def load_txt_data(file_path: str) -> np.ndarray:
    """
    读取单个txt文件，返回6x6xn_timestamp的numpy数组
    
    Args:
        file_path: Path to the .txt file
        
    Returns:
        numpy array of shape (6, 6, n_timestamp) representing MCG data
    """
    try:
        # 使用pandas读取制表符分隔的文件，无header，处理读取错误
        try:
            data = pd.read_csv(file_path, sep='\t', header=None).values  # shape: (n_timestamp, 37)
        except pd.errors.ParserError:
            # 如果C引擎失败，尝试使用Python引擎
            logger.warning(f"Using Python engine for {file_path} due to parsing error")
            data = pd.read_csv(file_path, sep='\t', header=None, engine='python').values
        
        # 去除第一列（基础idx通道），保留36个MCG通道
        data = data[:, 1:]  # shape: (n_timestamp, 36)
        
        # 转置得到 (36, n_timestamp)
        data = data.T  # shape: (36, n_timestamp)
        
        # 检查通道数
        if data.shape[0] != 36:
            raise ValueError(f"数据通道数不是36，实际为{data.shape[0]}，文件：{file_path}")
        
        # 转为6x6xn_timestamp
        data_reshaped = data.reshape(6, 6, -1)
        
        logger.debug(f"成功读取MCG数据：{file_path}，形状：{data_reshaped.shape}")
        return data_reshaped
        
    except Exception as e:
        logger.error(f"Error loading MCG data from {file_path}: {str(e)}")
        raise


def load_excel_data(excel_path: str, train_sheet: str, test_sheet: str, vessel_info_sheet: str, 
                   timepoint_file: Optional[str] = None) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, Optional[pd.DataFrame]]:
    """
    Load data from Excel file with multiple sheets.
    
    Args:
        excel_path: Path to the Excel file
        train_sheet: Name of training set sheet
        test_sheet: Name of test set sheet
        vessel_info_sheet: Name of vessel information sheet
        timepoint_file: Optional path to timepoint data file
        
    Returns:
        Tuple of (train_df, test_df, vessel_info_df, timepoint_df)
    """
    try:
        # Load all sheets
        train_df = pd.read_excel(excel_path, sheet_name=train_sheet)
        test_df = pd.read_excel(excel_path, sheet_name=test_sheet)
        vessel_info_df = pd.read_excel(excel_path, sheet_name=vessel_info_sheet)
        
        logger.info(f"Loaded training data: {len(train_df)} samples")
        logger.info(f"Loaded test data: {len(test_df)} samples")
        logger.info(f"Loaded vessel info: {len(vessel_info_df)} samples")
        
        # Load timepoint data if provided
        timepoint_df = None
        if timepoint_file and os.path.exists(timepoint_file):
            timepoint_df = load_timepoint_data(timepoint_file)
            logger.info(f"Loaded timepoint data: {len(timepoint_df) if timepoint_df is not None else 0} samples")
        
        return train_df, test_df, vessel_info_df, timepoint_df
        
    except Exception as e:
        logger.error(f"Error loading Excel file {excel_path}: {str(e)}")
        raise


def load_timepoint_data(timepoint_file: str) -> pd.DataFrame:
    """
    Load timepoint data from Excel file.
    
    Args:
        timepoint_file: Path to timepoint Excel file
        
    Returns:
        Combined dataframe with timepoint information
    """
    try:
        # Load both sheets from timepoint file
        sheet1_df = pd.read_excel(timepoint_file, sheet_name='Sheet1')
        sheet2_df = pd.read_excel(timepoint_file, sheet_name='Sheet2')
        
        # Standardize column names - Sheet2 uses '新增数据' instead of '心磁号'
        sheet2_df = sheet2_df.rename(columns={'新增数据': '心磁号'})
        
        # Combine both sheets
        combined_timepoint_df = pd.concat([sheet1_df, sheet2_df], ignore_index=True)
        
        # Ensure we have the expected columns
        expected_timepoint_cols = ['心磁号', 'Qp', 'Rp', 'Sp', 'To', 'Tp', 'Te']
        missing_cols = [col for col in expected_timepoint_cols if col not in combined_timepoint_df.columns]
        if missing_cols:
            logger.warning(f"Missing timepoint columns: {missing_cols}")
        
        logger.info(f"Loaded combined timepoint data: {len(combined_timepoint_df)} samples")
        logger.info(f"Timepoint columns: {list(combined_timepoint_df.columns)}")
        
        return combined_timepoint_df
        
    except Exception as e:
        logger.error(f"Error loading timepoint data from {timepoint_file}: {str(e)}")
        return None


def merge_datasets(train_df: pd.DataFrame, test_df: pd.DataFrame, vessel_info_df: pd.DataFrame, 
                  timepoint_df: Optional[pd.DataFrame] = None) -> pd.DataFrame:
    """
    Merge all datasets on '心磁号' ID.
    
    Args:
        train_df: Training set dataframe
        test_df: Test set dataframe
        vessel_info_df: Vessel information dataframe
        timepoint_df: Optional timepoint dataframe
        
    Returns:
        Merged dataframe with all information
    """
    # Add dataset split indicator
    train_df = train_df.copy()
    test_df = test_df.copy()
    train_df['split'] = 'train'
    test_df['split'] = 'test'
    
    # Remove vessel columns from train/test data to avoid conflicts
    # We only want to use vessel data from the vessel_info_df
    vessel_columns_to_remove = ['LM', 'LAD', 'LCX', 'RCA', '%LM', 'LAD.1', 'LCX.1', 'RCA.1']
    
    for col in vessel_columns_to_remove:
        if col in train_df.columns:
            train_df = train_df.drop(columns=[col])
            logger.info(f"Removed conflicting column {col} from training set")
        if col in test_df.columns:
            test_df = test_df.drop(columns=[col])
            logger.info(f"Removed conflicting column {col} from test set")
    
    # Combine train and test
    combined_df = pd.concat([train_df, test_df], ignore_index=True)
    
    # Merge with vessel information (now no conflicts)
    merged_df = combined_df.merge(vessel_info_df, on='心磁号', how='left')
    
    # Filter out samples without soft labels (vessel information)
    vessel_columns = ['LM', 'LAD', 'LCX', 'RCA']
    has_soft_labels = merged_df[vessel_columns].notna().any(axis=1)
    samples_before_filter = len(merged_df)
    merged_df = merged_df[has_soft_labels].reset_index(drop=True)
    samples_after_filter = len(merged_df)
    
    logger.info(f"Filtered samples without soft labels: {samples_before_filter} -> {samples_after_filter} "
               f"(removed {samples_before_filter - samples_after_filter} samples)")
    
    # Merge with timepoint information if available
    if timepoint_df is not None:
        merged_df = merged_df.merge(timepoint_df, on='心磁号', how='left')
        
        # Filter out samples without timepoint information
        timepoint_columns = ['Qp', 'Rp', 'Sp', 'To', 'Tp', 'Te']
        has_timepoints = merged_df[timepoint_columns].notna().any(axis=1)
        samples_before_timepoint_filter = len(merged_df)
        merged_df = merged_df[has_timepoints].reset_index(drop=True)
        samples_after_timepoint_filter = len(merged_df)
        
        logger.info(f"Filtered samples without timepoint info: {samples_before_timepoint_filter} -> {samples_after_timepoint_filter} "
                   f"(removed {samples_before_timepoint_filter - samples_after_timepoint_filter} samples)")
        logger.info(f"Timepoint columns available: {timepoint_columns}")
    
    logger.info(f"Final merged dataset: {len(merged_df)} total samples")
    logger.info(f"Train samples: {len(merged_df[merged_df['split'] == 'train'])}")
    logger.info(f"Test samples: {len(merged_df[merged_df['split'] == 'test'])}")
    
    # Log vessel columns from vessel info
    vessel_info_cols = [col for col in vessel_info_df.columns if any(v in col.upper() for v in ['LM', 'LAD', 'LCX', 'RCA'])]
    logger.info(f"Vessel columns from 训测集三支信息: {vessel_info_cols}")
    
    return merged_df


def process_clinical_features(df: pd.DataFrame, fit_scaler: bool = True, scaler: Optional[StandardScaler] = None) -> Tuple[np.ndarray, Optional[StandardScaler], List[str]]:
    """
    Process clinical features: handle missing values and encode categorical variables.
    
    Args:
        df: Dataframe with clinical features
        fit_scaler: Whether to fit a new scaler
        scaler: Pre-fitted scaler to use for transformation
        
    Returns:
        Tuple of (processed_features, scaler, feature_names)
    """
    # Define clinical feature columns
    feature_columns = [
        '临床特征-身高', '临床特征-体重', '临床特征-性别', '临床特征-年龄',
        '临床特征-吸烟', '临床特征-饮酒', '临床特征-高血压', '临床特征-糖尿病',
        '临床特征-高脂血症', '临床特征-典型症状', '临床特征-既往介入', '临床特征-所在医院'
    ]
    
    # Check which columns exist
    existing_columns = [col for col in feature_columns if col in df.columns]
    logger.info(f"Found clinical features: {existing_columns}")
    
    if not existing_columns:
        logger.warning("No clinical features found in the dataset")
        return np.array([]), None, []
    
    # Extract features
    features_df = df[existing_columns].copy()
    
    # Handle categorical features
    categorical_features = ['临床特征-性别', '临床特征-所在医院']
    categorical_features = [col for col in categorical_features if col in existing_columns]
    
    processed_features = []
    feature_names = []
    
    for col in existing_columns:
        if col in categorical_features:
            # Handle categorical features with one-hot encoding
            if col == '临床特征-性别':
                # Fill missing values with mode
                mode_value = features_df[col].mode()[0] if not features_df[col].mode().empty else '男'
                features_df[col] = features_df[col].fillna(mode_value)
                
                # Convert to numeric (男=1, 女=0)
                gender_encoded = (features_df[col] == '男').astype(int)
                processed_features.append(gender_encoded.values.reshape(-1, 1))
                feature_names.append(f'{col}_encoded')
                
            elif col == '临床特征-所在医院':
                # Use label encoding for hospitals
                le = LabelEncoder()
                # Fill missing values with 'Unknown'
                features_df[col] = features_df[col].fillna('Unknown')
                hospital_encoded = le.fit_transform(features_df[col])
                processed_features.append(hospital_encoded.reshape(-1, 1))
                feature_names.append(f'{col}_encoded')
        else:
            # Handle numerical features
            # Fill missing values with -1 (as requested)
            features_df[col] = features_df[col].fillna(-1.0)
            processed_features.append(features_df[col].values.reshape(-1, 1))
            feature_names.append(col)
    
    if processed_features:
        # Combine all features
        combined_features = np.concatenate(processed_features, axis=1)
        
        # Apply scaling
        if fit_scaler:
            scaler = StandardScaler()
            scaled_features = scaler.fit_transform(combined_features)
        else:
            if scaler is not None:
                scaled_features = scaler.transform(combined_features)
            else:
                scaled_features = combined_features
                
        return scaled_features, scaler, feature_names
    else:
        return np.array([]), None, []


def compute_risk_scores(df: pd.DataFrame, vessel_targets: dict = None) -> pd.DataFrame:
    """
    Compute risk scores from stenosis values using sigmoid transformation.
    
    Args:
        df: Dataframe containing vessel columns
        vessel_targets: Configuration for vessel targets
        
    Returns:
        Dataframe with added risk score columns
    """
    # Default vessel configuration
    if vessel_targets is None:
        vessel_targets = {
            'vessels': ['LM', 'LAD', 'LCX', 'RCA'],
            'use_three_vessel': False
        }
    
    # Get configured vessels
    vessels = vessel_targets.get('vessels', ['LM', 'LAD', 'LCX', 'RCA'])
    
    # Apply three-vessel mode if enabled
    if vessel_targets.get('use_three_vessel', False):
        vessels = [v for v in vessels if v != 'LM']  # 排除LM
    
    # Map vessel column names to standardized risk score names
    vessel_column_mapping = {
        vessel: f'{vessel}_risk_score' for vessel in vessels
    }
    
    df = df.copy()
    
    for vessel_col, risk_col in vessel_column_mapping.items():
        if vessel_col in df.columns:
            # Get vessel data and handle NaN values
            vessel_data = df[vessel_col]
            valid_count = vessel_data.notna().sum()
            total_count = len(vessel_data)
            
            # Apply sigmoid transformation: sigmoid((stenosis - 50) / 10)
            # Only apply to non-NaN values
            risk_score = pd.Series(index=df.index, dtype=float)
            valid_mask = vessel_data.notna()
            
            if valid_mask.any():
                risk_score[valid_mask] = 1 / (1 + np.exp(-((vessel_data[valid_mask] - 50) / 10)))
            
            df[risk_col] = risk_score
            logger.info(f"Computed {risk_col} from {vessel_col}: {valid_count}/{total_count} valid values")
        else:
            logger.warning(f"Vessel column {vessel_col} not found in dataset")
            df[risk_col] = np.nan
    
    return df


def get_available_mcg_files(txt_data_dir: str, mcg_ids: List[str]) -> Tuple[List[str], List[str]]:
    """
    Check which MCG files are available for the given IDs.
    
    Args:
        txt_data_dir: Directory containing .txt MCG files
        mcg_ids: List of MCG IDs to check
        
    Returns:
        Tuple of (available_ids, missing_ids)
    """
    available_ids = []
    missing_ids = []
    
    for mcg_id in mcg_ids:
        file_path = os.path.join(txt_data_dir, f"{mcg_id}.txt")
        if os.path.exists(file_path):
            available_ids.append(mcg_id)
        else:
            missing_ids.append(mcg_id)
    
    logger.info(f"Available MCG files: {len(available_ids)}/{len(mcg_ids)}")
    if missing_ids:
        logger.warning(f"Missing MCG files for IDs: {missing_ids[:10]}...")  # Show first 10
    
    return available_ids, missing_ids


def create_timepoint_channel(timepoints: Dict[str, int], sequence_length: int, 
                           gaussian_sigma: float = 20.0) -> np.ndarray:
    """
    Create a timepoint feature channel with Gaussian kernel smoothing.
    
    Args:
        timepoints: Dictionary with timepoint names and their positions
                   e.g., {'Qp': 383, 'Rp': 416, 'Sp': 445, 'To': 654, 'Tp': 704, 'Te': 742}
        sequence_length: Length of the sequence (max 2000)
        gaussian_sigma: Standard deviation for Gaussian kernel
        
    Returns:
        1D array of shape (sequence_length,) with Gaussian peaks at timepoint positions
    """
    # Initialize channel with zeros
    timepoint_channel = np.zeros(sequence_length, dtype=np.float32)
    
    # Create Gaussian peaks at each timepoint position
    for timepoint_name, position in timepoints.items():
        if pd.isna(position) or position < 0 or position >= sequence_length:
            continue
            
        # Create Gaussian kernel centered at position
        x = np.arange(sequence_length)
        gaussian_peak = np.exp(-0.5 * ((x - position) / gaussian_sigma) ** 2)
        
        # Add to channel (accumulate if timepoints overlap)
        timepoint_channel += gaussian_peak
    
    # Normalize to [0, 1] range
    if timepoint_channel.max() > 0:
        timepoint_channel = timepoint_channel / timepoint_channel.max()
    
    return timepoint_channel


def extract_timepoint_features(mcg_data: np.ndarray, timepoint_info: Dict[str, int], 
                              gaussian_config: dict = None) -> np.ndarray:
    """
    Extract timepoint features and add as 37th channel to MCG data.
    
    Args:
        mcg_data: Original MCG data of shape (6, 6, n_timestamp)
        timepoint_info: Dictionary with timepoint positions
        gaussian_config: Configuration for Gaussian smoothing
        
    Returns:
        Enhanced MCG data of shape (37, n_timestamp) with timepoint channel as 37th channel
    """
    # Default gaussian configuration
    if gaussian_config is None:
        gaussian_config = {'sigma': 20.0, 'enabled': True}
    
    sequence_length = mcg_data.shape[2]
    
    # Flatten original 36 channels from (6, 6, n_timestamp) to (36, n_timestamp)
    original_channels = mcg_data.reshape(36, sequence_length)
    
    # Create timepoint channel
    if gaussian_config.get('enabled', True):
        gaussian_sigma = gaussian_config.get('sigma', 20.0)
        timepoint_channel = create_timepoint_channel(timepoint_info, sequence_length, gaussian_sigma)
    else:
        # Without Gaussian smoothing - just use discrete timepoints
        timepoint_channel = np.zeros(sequence_length)
        for tp_name, tp_idx in timepoint_info.items():
            if 0 <= tp_idx < sequence_length:
                timepoint_channel[tp_idx] = 1.0
    
    # Add timepoint channel as 37th channel: shape becomes (37, n_timestamp)
    enhanced_mcg_data = np.vstack([original_channels, timepoint_channel.reshape(1, -1)])
    
    return enhanced_mcg_data


class MCGDataLoader:
    """Main data loader class for MCG project."""
    
    def __init__(self, excel_path: str, txt_data_dir: str, train_sheet: str = "训练集", 
                 test_sheet: str = "内测集", vessel_info_sheet: str = "训测集三支信息",
                 timepoint_file: Optional[str] = None, vessel_targets: dict = None,
                 timepoint_gaussian: dict = None):
        self.excel_path = excel_path
        self.txt_data_dir = txt_data_dir
        self.train_sheet = train_sheet
        self.test_sheet = test_sheet
        self.vessel_info_sheet = vessel_info_sheet
        self.timepoint_file = timepoint_file
        self.vessel_targets = vessel_targets or {'vessels': ['LM', 'LAD', 'LCX', 'RCA'], 'use_three_vessel': False}
        self.timepoint_gaussian = timepoint_gaussian or {'sigma': 20.0, 'enabled': True}
        
        self.merged_df = None
        self.clinical_scaler = None
        self.feature_names = None
        self.timepoint_df = None
        
    def load_and_process_data(self) -> pd.DataFrame:
        """Load and process all data."""
        # Load Excel data
        train_df, test_df, vessel_info_df, timepoint_df = load_excel_data(
            self.excel_path, self.train_sheet, self.test_sheet, self.vessel_info_sheet, self.timepoint_file
        )
        
        # Store timepoint data
        self.timepoint_df = timepoint_df
        
        # Merge datasets
        self.merged_df = merge_datasets(train_df, test_df, vessel_info_df, timepoint_df)
        
        # Compute risk scores
        self.merged_df = compute_risk_scores(self.merged_df, self.vessel_targets)
        
        # Process clinical features for training set
        train_mask = self.merged_df['split'] == 'train'
        train_features, self.clinical_scaler, self.feature_names = process_clinical_features(
            self.merged_df[train_mask], fit_scaler=True
        )
        
        # Process clinical features for test set
        test_features, _, _ = process_clinical_features(
            self.merged_df[~train_mask], fit_scaler=False, scaler=self.clinical_scaler
        )
        
        # Add processed features back to dataframe
        if train_features.size > 0:
            # Create feature columns
            feature_cols = [f'clinical_feature_{i}' for i in range(train_features.shape[1])]
            
            # Initialize feature columns
            for col in feature_cols:
                self.merged_df[col] = np.nan
            
            # Fill train features
            self.merged_df.loc[train_mask, feature_cols] = train_features
            
            # Fill test features
            if test_features.size > 0:
                self.merged_df.loc[~train_mask, feature_cols] = test_features
        
        # Check available MCG files
        available_ids, missing_ids = get_available_mcg_files(
            self.txt_data_dir, self.merged_df['心磁号'].tolist()
        )
        
        # Filter to only available MCG files
        self.merged_df = self.merged_df[self.merged_df['心磁号'].isin(available_ids)].reset_index(drop=True)
        
        logger.info(f"Final dataset size: {len(self.merged_df)} samples")
        
        return self.merged_df
    
    def get_train_test_split(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Get train and test dataframes."""
        if self.merged_df is None:
            raise ValueError("Data not loaded. Call load_and_process_data() first.")
        
        train_df = self.merged_df[self.merged_df['split'] == 'train'].copy()
        test_df = self.merged_df[self.merged_df['split'] == 'test'].copy()
        
        return train_df, test_df
    
    def get_train_data(self) -> pd.DataFrame:
        """Get training dataframe."""
        if self.merged_df is None:
            raise ValueError("Data not loaded. Call load_and_process_data() first.")
        
        return self.merged_df[self.merged_df['split'] == 'train'].copy()