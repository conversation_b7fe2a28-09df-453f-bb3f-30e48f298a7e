# 42通道多模态CNN+GRU模型配置 (支持空间感知)

# 模型架构类型
architecture: "multimodal_cnn_gru"

# 空间处理配置
spatial:
  enabled: false  # 设为true启用空间感知处理
  upsample_to_36x36: false  # 设为true使用36x36插值
  preserve_6x6: true  # 保持6x6空间结构

# 信号CNN处理器配置
signal_cnn:
  base_channels: 64
  num_blocks: 6
  dropout: 0.2

# 时刻点处理器配置  
timepoint:
  hidden_dim: 128
  num_layers: 3
  dropout: 0.1

# GRU编码器配置
gru:
  hidden_dim: 256
  num_layers: 3
  dropout: 0.2

# 跨模态注意力融合
fusion:
  num_heads: 8
  dropout: 0.1

# 临床特征处理
clinical:
  hidden_dim: 128
  dropout: 0.3

# 最终特征融合
final_fusion:
  hidden_dim: 512
  dropout: 0.3

# 多任务输出头
output:
  risk_head_hidden: 256
  conclusion_head_hidden: 128
  dropout: 0.2