
# Data Configuration
data:
  xlsx_file: "data/软标签建模_训测集.xlsx"  # Path to Excel file with labels and clinical features
  txt_data_dir: "/home/<USER>/Wangmeng/data/data0318"  # Directory containing .txt MCG signal files
  timepoint_file: "数据和时刻20250520.xlsx"  # Path to timepoint data file
  max_sequence_length: 1200  # Maximum sequence length for padding
  train_sheet: "训练集"
  test_sheet: "内测集"
  vessel_info_sheet: "训测集三支信息"
  
  # 血管目标配置 (可配置3支vs4支血管)
  vessel_targets:
    vessels: ["LM", "LAD", "LCX", "RCA"]  # 4支血管，如需3支可改为 ["LAD", "LCX", "RCA"]
    conclusion_field: "造影结论"  # 结论字段名，可配置为其他字段
    use_three_vessel: false  # 设为true时自动排除LM，只使用3支血管
  
  # 时刻点高斯平滑参数
  timepoint_gaussian:
    sigma: 30.0  # 高斯核标准差，影响平滑程度(优化后的值)
    enabled: true  # 是否启用高斯平滑
  
  # Preprocessing
  preprocessing:
    normalization: "standard"  # Options: "standard", "maxabs", "minmax" (改为标准归一化)
    padding_strategy: "zeros"  # Options: "zeros", "reflect", "edge"
    clinical_feature_imputation: "median"  # Options: "median", "mean", "mode"
    use_training_set_stats: true  # Whether to use training set statistics for normalization
    
  # Spatial preprocessing (new)
  spatial:
    enabled: false  # Enable spatial-aware processing
    upsample_to_36x36: false  # Use bicubic interpolation to upsample 6x6 to 36x36
    preserve_6x6: true  # Preserve original 6x6 spatial structure
    
  # Data augmentation (optional)
  augmentation:
    enabled: True
    noise_std: 0.1
    mixup_alpha: 0.2
    smote_k_neighbors: 5
    augmentation_factor: 3  # How many augmented versions per original sample

# Model Configuration
model:
  # 42通道多模态模型配置文件路径
  model_config_path: "configs/models/spatial_cnn_gru_6x6.yaml"  # 可选: multimodal_cnn_gru.yaml, spatial_cnn_gru_6x6.yaml, spatial_unet_6x6.yaml等
  
  # 兼容性配置 - 直接指定架构 (如果没有指定model_config_path)
  architecture: "multimodal_unet"  # 42通道模型架构类型

# Training Configuration
training:
  batch_size: 32
  num_epochs: 150  # 测试用较少轮次，实际训练可调整为150
  learning_rate: 3e-5
  weight_decay: 3e-5
  
  # Optimizer
  optimizer:
    type: "NAdam"  # Options: "Adam", "AdamW", "SGD", "NAdam"
    betas: [0.9, 0.999]
    momentum_decay: 4e-3  # For NAdam optimizer
    
  # Learning rate scheduler
  scheduler:
    type: "cosine"  # Options: "cosine", "step", "plateau"
    warmup_epochs: 5
    min_lr: 5e-6
    
  # Loss function weights
  loss_weights:
    # 风险类型配置: "regression" 或 "classification"
    risk_type: "classification"  # "regression"(回归) 或 "classification"(分类)
    risk_threshold: 70.0  # 风险分类阈值(百分比，仅在risk_type="classification"时生效)
    
    # 损失权重
    risk_score_weight: 0.5  # 平衡权重
    conclusion_weight: 1.5  # 平衡权重
    
    # Focal Loss参数
    focal_loss_alpha: 0.75
    focal_loss_gamma: 1.5  # 优化后的gamma值
    
  # Gradient accumulation
  gradient_accumulation_steps: 1
  grad_clip_norm: 1.0
    
  # Early stopping
  early_stopping:
    patience: 50
    min_delta: 1e-4
    
  # Checkpointing
  checkpoint:
    save_every_n_epochs: 5
    keep_best_only: true

# Evaluation Configuration
evaluation:
  metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1"
    - "auc"
    - "sensitivity"
    - "specificity"
    - "mse"
    - "mae"
    
  # Visualization
  visualization:
    plot_training_curves: true
    plot_confusion_matrix: true
    plot_roc_curves: true

# Hardware Configuration
hardware:
  device: "auto"  # Options: "auto", "cuda", "cpu"
  num_workers: 4
  pin_memory: true

# Logging Configuration
logging:
  log_dir: "logs"
  tensorboard_dir: "logs/tensorboard"
  checkpoint_dir: "checkpoints"
  log_level: "debug"
  save_predictions: true