"""
Spatial-aware multimodal models that handle 42-channel data with spatial processing
for the first 36 channels and timepoint processing for the last 6 channels.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, Tuple
import math


class SpatialMultiModalCNNGRU(nn.Module):
    """
    Spatial-aware CNN+GRU model for 42-channel MCG data.
    
    Architecture:
    - First 36 channels: Reshaped to 6x6 spatial format, processed with 2D CNN
    - Last 6 channels: Processed as 1D timepoint features with CNN+GRU
    - Clinical features: Processed separately and fused
    """
    
    def __init__(self, clinical_feature_dim: int = 12, config: Dict[str, Any] = None):
        super().__init__()
        
        self.config = config or {}
        self.clinical_feature_dim = clinical_feature_dim
        
        # Spatial configuration
        spatial_config = self.config.get('spatial', {})
        self.use_36x36 = spatial_config.get('upsample_to_36x36', False)
        spatial_size = 36 if self.use_36x36 else 6
        
        # 1. Spatial CNN for first 36 channels (reshaped to spatial format)
        self.spatial_cnn = self._build_spatial_cnn(spatial_size)
        
        # 2. Timepoint CNN+GRU for last 6 channels  
        self.timepoint_cnn = nn.Sequential(
            nn.Conv1d(6, 32, kernel_size=7, padding=3),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.Conv1d(32, 64, kernel_size=5, padding=2),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(300)  # Reduce to fixed length
        )
        
        self.timepoint_gru = nn.GRU(
            input_size=64,
            hidden_size=128,
            num_layers=2,
            batch_first=True,
            dropout=0.2,
            bidirectional=True
        )
        
        # 3. Clinical feature processing
        self.clinical_fc = nn.Sequential(
            nn.Linear(clinical_feature_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32)
        )
        
        # 4. Feature fusion
        spatial_features = 256  # From spatial CNN
        timepoint_features = 256  # From GRU (128 * 2 for bidirectional)
        clinical_features = 32
        
        self.fusion_fc = nn.Sequential(
            nn.Linear(spatial_features + timepoint_features + clinical_features, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 5. Output heads
        self.risk_head = nn.Linear(128, 4)  # 4 vessel risk scores
        self.conclusion_head = nn.Linear(128, 1)  # Binary conclusion
        
    def _build_spatial_cnn(self, spatial_size: int) -> nn.Module:
        """Build spatial CNN for processing spatial MCG data"""
        
        if spatial_size == 6:
            # For 6x6 input - use smaller kernels and less pooling
            return nn.Sequential(
                # First block
                nn.Conv2d(1, 32, kernel_size=3, padding=1),
                nn.BatchNorm2d(32),
                nn.ReLU(),
                nn.Conv2d(32, 64, kernel_size=3, padding=1),
                nn.BatchNorm2d(64),
                nn.ReLU(),
                
                # Second block - no pooling to preserve small spatial size
                nn.Conv2d(64, 128, kernel_size=3, padding=1),
                nn.BatchNorm2d(128),
                nn.ReLU(),
                nn.Conv2d(128, 256, kernel_size=3, padding=1),
                nn.BatchNorm2d(256),
                nn.ReLU(),
                
                # Global pooling
                nn.AdaptiveAvgPool2d(1),
                nn.Flatten(),
                nn.Linear(256, 256)
            )
        else:  # 36x36
            # For 36x36 input - can use more traditional CNN with pooling
            return nn.Sequential(
                # First block
                nn.Conv2d(1, 32, kernel_size=5, padding=2),
                nn.BatchNorm2d(32),
                nn.ReLU(),
                nn.MaxPool2d(2),  # 36x36 -> 18x18
                
                # Second block
                nn.Conv2d(32, 64, kernel_size=3, padding=1),
                nn.BatchNorm2d(64),
                nn.ReLU(),
                nn.MaxPool2d(2),  # 18x18 -> 9x9
                
                # Third block
                nn.Conv2d(64, 128, kernel_size=3, padding=1),
                nn.BatchNorm2d(128),
                nn.ReLU(),
                nn.MaxPool2d(3),  # 9x9 -> 3x3
                
                # Fourth block
                nn.Conv2d(128, 256, kernel_size=3, padding=1),
                nn.BatchNorm2d(256),
                nn.ReLU(),
                nn.AdaptiveAvgPool2d(1),
                nn.Flatten(),
                nn.Linear(256, 256)
            )
    
    def forward(self, mcg_data: torch.Tensor, clinical_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Forward pass
        
        Args:
            mcg_data: [batch_size, seq_len, 42] - 42 channel MCG data
            clinical_features: [batch_size, clinical_dim] - Clinical features
            
        Returns:
            Dict with 'risk_scores' and 'conclusion' keys
        """
        batch_size, seq_len, channels = mcg_data.shape
        
        # Split the 42 channels: first 36 for spatial, last 6 for timepoint
        spatial_data = mcg_data[:, :, :36]  # [batch, seq_len, 36]
        timepoint_data = mcg_data[:, :, 36:]  # [batch, seq_len, 6]
        
        # 1. Process spatial data (first 36 channels)
        # Reshape to spatial format: [batch * seq_len, 6, 6] or [batch * seq_len, 36, 36]
        if self.use_36x36:
            # Apply bicubic interpolation from 6x6 to 36x36
            spatial_reshaped = spatial_data.view(batch_size * seq_len, 6, 6)
            spatial_reshaped = spatial_reshaped.unsqueeze(1)  # Add channel dim
            spatial_reshaped = F.interpolate(spatial_reshaped, size=(36, 36), mode='bicubic', align_corners=True)
        else:
            # Use original 6x6
            spatial_reshaped = spatial_data.view(batch_size * seq_len, 1, 6, 6)
        
        # Apply spatial CNN
        spatial_features = self.spatial_cnn(spatial_reshaped)  # [batch * seq_len, 256]
        spatial_features = spatial_features.view(batch_size, seq_len, -1)  # [batch, seq_len, 256]
        
        # Temporal pooling for spatial features
        spatial_features = spatial_features.mean(dim=1)  # [batch, 256]
        
        # 2. Process timepoint data (last 6 channels)
        timepoint_data = timepoint_data.transpose(1, 2)  # [batch, 6, seq_len]
        timepoint_features = self.timepoint_cnn(timepoint_data)  # [batch, 64, 300]
        timepoint_features = timepoint_features.transpose(1, 2)  # [batch, 300, 64]
        
        # Apply GRU
        timepoint_output, _ = self.timepoint_gru(timepoint_features)  # [batch, 300, 256]
        timepoint_features = timepoint_output[:, -1, :]  # Take last output [batch, 256]
        
        # 3. Process clinical features
        clinical_out = self.clinical_fc(clinical_features)  # [batch, 32]
        
        # 4. Fusion
        fused_features = torch.cat([spatial_features, timepoint_features, clinical_out], dim=1)
        fused_features = self.fusion_fc(fused_features)  # [batch, 128]
        
        # 5. Output predictions
        risk_scores = torch.sigmoid(self.risk_head(fused_features))  # [batch, 4] - probabilities
        conclusion_logits = self.conclusion_head(fused_features)  # [batch, 1] - raw logits
        
        return {
            'risk_scores': risk_scores,
            'conclusion_logits': conclusion_logits
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        spatial_type = "36x36" if self.use_36x36 else "6x6"
        
        return {
            'architecture': f'SpatialMultiModalCNNGRU_{spatial_type}',
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'spatial_size': spatial_type,
            'clinical_feature_dim': self.clinical_feature_dim
        }


class SpatialMultiModalUNet(nn.Module):
    """
    Spatial-aware U-Net model for 42-channel MCG data.
    
    Uses U-Net architecture for spatial processing and separate path for timepoint features.
    """
    
    def __init__(self, clinical_feature_dim: int = 12, config: Dict[str, Any] = None):
        super().__init__()
        
        self.config = config or {}
        self.clinical_feature_dim = clinical_feature_dim
        
        # Spatial configuration
        spatial_config = self.config.get('spatial', {})
        self.use_36x36 = spatial_config.get('upsample_to_36x36', False)
        spatial_size = 36 if self.use_36x36 else 6
        
        # 1. U-Net for spatial processing
        self.spatial_unet = self._build_spatial_unet(spatial_size)
        
        # 2. Timepoint processing
        self.timepoint_processor = nn.Sequential(
            nn.Conv1d(6, 64, kernel_size=7, padding=3),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, kernel_size=5, padding=2),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(128, 128)
        )
        
        # 3. Clinical feature processing
        self.clinical_fc = nn.Sequential(
            nn.Linear(clinical_feature_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32)
        )
        
        # 4. Feature fusion
        spatial_features = 256
        timepoint_features = 128
        clinical_features = 32
        
        self.fusion_fc = nn.Sequential(
            nn.Linear(spatial_features + timepoint_features + clinical_features, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 5. Output heads
        self.risk_head = nn.Linear(128, 4)
        self.conclusion_head = nn.Linear(128, 1)
    
    def _build_spatial_unet(self, spatial_size: int) -> nn.Module:
        """Build U-Net for spatial processing"""
        
        if spatial_size == 6:
            # Simplified U-Net for small 6x6 input
            return SpatialUNet6x6()
        else:
            # Full U-Net for 36x36 input
            return SpatialUNet36x36()
    
    def forward(self, mcg_data: torch.Tensor, clinical_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Forward pass"""
        batch_size, seq_len, channels = mcg_data.shape
        
        # Split channels
        spatial_data = mcg_data[:, :, :36]
        timepoint_data = mcg_data[:, :, 36:]
        
        # 1. Process spatial data
        if self.use_36x36:
            spatial_reshaped = spatial_data.view(batch_size * seq_len, 6, 6)
            spatial_reshaped = spatial_reshaped.unsqueeze(1)
            spatial_reshaped = F.interpolate(spatial_reshaped, size=(36, 36), mode='bicubic', align_corners=True)
        else:
            spatial_reshaped = spatial_data.view(batch_size * seq_len, 1, 6, 6)
        
        spatial_features = self.spatial_unet(spatial_reshaped)
        spatial_features = spatial_features.view(batch_size, seq_len, -1)
        spatial_features = spatial_features.mean(dim=1)
        
        # 2. Process timepoint data
        timepoint_data = timepoint_data.transpose(1, 2)
        timepoint_features = self.timepoint_processor(timepoint_data)
        
        # 3. Process clinical features
        clinical_out = self.clinical_fc(clinical_features)
        
        # 4. Fusion and output
        fused_features = torch.cat([spatial_features, timepoint_features, clinical_out], dim=1)
        fused_features = self.fusion_fc(fused_features)
        
        risk_scores = torch.sigmoid(self.risk_head(fused_features))
        conclusion_logits = self.conclusion_head(fused_features)
        
        return {
            'risk_scores': risk_scores,
            'conclusion_logits': conclusion_logits
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        spatial_type = "36x36" if self.use_36x36 else "6x6"
        
        return {
            'architecture': f'SpatialMultiModalUNet_{spatial_type}',
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'spatial_size': spatial_type,
            'clinical_feature_dim': self.clinical_feature_dim
        }


class SpatialMultiModalViT(nn.Module):
    """
    Spatial-aware Vision Transformer model for 42-channel MCG data.
    
    Uses ViT for spatial processing and separate path for timepoint features.
    """
    
    def __init__(self, clinical_feature_dim: int = 12, config: Dict[str, Any] = None):
        super().__init__()
        
        self.config = config or {}
        self.clinical_feature_dim = clinical_feature_dim
        
        # Spatial configuration
        spatial_config = self.config.get('spatial', {})
        self.use_36x36 = spatial_config.get('upsample_to_36x36', False)
        spatial_size = 36 if self.use_36x36 else 6
        
        # 1. Vision Transformer for spatial processing
        self.spatial_vit = self._build_spatial_vit(spatial_size)
        
        # 2. Timepoint processing (same as UNet)
        self.timepoint_processor = nn.Sequential(
            nn.Conv1d(6, 64, kernel_size=7, padding=3),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, kernel_size=5, padding=2),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(128, 128)
        )
        
        # 3. Clinical feature processing
        self.clinical_fc = nn.Sequential(
            nn.Linear(clinical_feature_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32)
        )
        
        # 4. Feature fusion
        spatial_features = 256
        timepoint_features = 128
        clinical_features = 32
        
        self.fusion_fc = nn.Sequential(
            nn.Linear(spatial_features + timepoint_features + clinical_features, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 5. Output heads
        self.risk_head = nn.Linear(128, 4)
        self.conclusion_head = nn.Linear(128, 1)
    
    def _build_spatial_vit(self, spatial_size: int) -> nn.Module:
        """Build Vision Transformer for spatial processing"""
        
        if spatial_size == 6:
            # Mini ViT for 6x6 input
            return SpatialViT6x6()
        else:
            # Standard ViT for 36x36 input
            return SpatialViT36x36()
    
    def forward(self, mcg_data: torch.Tensor, clinical_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Forward pass"""
        batch_size, seq_len, channels = mcg_data.shape
        
        # Split channels
        spatial_data = mcg_data[:, :, :36]
        timepoint_data = mcg_data[:, :, 36:]
        
        # 1. Process spatial data
        if self.use_36x36:
            spatial_reshaped = spatial_data.view(batch_size * seq_len, 6, 6)
            spatial_reshaped = spatial_reshaped.unsqueeze(1)
            spatial_reshaped = F.interpolate(spatial_reshaped, size=(36, 36), mode='bicubic', align_corners=True)
        else:
            spatial_reshaped = spatial_data.view(batch_size * seq_len, 1, 6, 6)
        
        spatial_features = self.spatial_vit(spatial_reshaped)
        spatial_features = spatial_features.view(batch_size, seq_len, -1)
        spatial_features = spatial_features.mean(dim=1)
        
        # 2. Process timepoint data
        timepoint_data = timepoint_data.transpose(1, 2)
        timepoint_features = self.timepoint_processor(timepoint_data)
        
        # 3. Process clinical features
        clinical_out = self.clinical_fc(clinical_features)
        
        # 4. Fusion and output
        fused_features = torch.cat([spatial_features, timepoint_features, clinical_out], dim=1)
        fused_features = self.fusion_fc(fused_features)
        
        risk_scores = torch.sigmoid(self.risk_head(fused_features))
        conclusion_logits = self.conclusion_head(fused_features)
        
        return {
            'risk_scores': risk_scores,
            'conclusion_logits': conclusion_logits
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        spatial_type = "36x36" if self.use_36x36 else "6x6"
        
        return {
            'architecture': f'SpatialMultiModalViT_{spatial_type}',
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'spatial_size': spatial_type,
            'clinical_feature_dim': self.clinical_feature_dim
        }


# Helper classes for spatial processing

class SpatialUNet6x6(nn.Module):
    """Simplified U-Net for 6x6 spatial data"""
    
    def __init__(self):
        super().__init__()
        
        # Encoder
        self.enc1 = nn.Sequential(
            nn.Conv2d(1, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU()
        )
        self.enc2 = nn.Sequential(
            nn.Conv2d(32, 64, 3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU()
        )
        
        # Decoder
        self.dec1 = nn.Sequential(
            nn.Conv2d(64, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU()
        )
        
        # Output
        self.final = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(32, 256)
        )
    
    def forward(self, x):
        # Encoder
        e1 = self.enc1(x)
        e2 = self.enc2(e1)
        
        # Decoder with skip connection
        d1 = self.dec1(e2) + e1  # Skip connection
        
        return self.final(d1)


class SpatialUNet36x36(nn.Module):
    """Full U-Net for 36x36 spatial data"""
    
    def __init__(self):
        super().__init__()
        
        # Encoder
        self.enc1 = self._conv_block(1, 32)
        self.enc2 = self._conv_block(32, 64)
        self.enc3 = self._conv_block(64, 128)
        
        self.pool = nn.MaxPool2d(2)
        
        # Decoder
        self.up3 = nn.ConvTranspose2d(128, 64, 2, 2)
        self.dec3 = self._conv_block(128, 64)
        
        self.up2 = nn.ConvTranspose2d(64, 32, 2, 2)
        self.dec2 = self._conv_block(64, 32)
        
        # Output
        self.final = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(32, 256)
        )
    
    def _conv_block(self, in_ch, out_ch):
        return nn.Sequential(
            nn.Conv2d(in_ch, out_ch, 3, padding=1),
            nn.BatchNorm2d(out_ch),
            nn.ReLU(),
            nn.Conv2d(out_ch, out_ch, 3, padding=1),
            nn.BatchNorm2d(out_ch),
            nn.ReLU()
        )
    
    def forward(self, x):
        # Encoder
        e1 = self.enc1(x)  # 36x36
        e2 = self.enc2(self.pool(e1))  # 18x18
        e3 = self.enc3(self.pool(e2))  # 9x9
        
        # Decoder
        d3 = self.up3(e3)  # 18x18
        d3 = torch.cat([e2, d3], dim=1)
        d3 = self.dec3(d3)
        
        d2 = self.up2(d3)  # 36x36
        d2 = torch.cat([e1, d2], dim=1)
        d2 = self.dec2(d2)
        
        return self.final(d2)


class SpatialViT6x6(nn.Module):
    """Mini Vision Transformer for 6x6 spatial data"""
    
    def __init__(self):
        super().__init__()
        
        # Patch embedding - use 2x2 patches for 6x6 input
        self.patch_embed = nn.Conv2d(1, 128, kernel_size=2, stride=2)  # 6x6 -> 3x3 patches
        self.pos_embed = nn.Parameter(torch.randn(1, 9, 128))
        
        # Transformer blocks
        self.transformer = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(
                d_model=128,
                nhead=8,
                dim_feedforward=256,
                dropout=0.1,
                batch_first=True
            ),
            num_layers=2
        )
        
        # Output
        self.norm = nn.LayerNorm(128)
        self.head = nn.Linear(128, 256)
    
    def forward(self, x):
        # Patch embedding
        x = self.patch_embed(x)  # [batch, 128, 3, 3]
        x = x.flatten(2).transpose(1, 2)  # [batch, 9, 128]
        
        # Add position embedding
        x = x + self.pos_embed
        
        # Transformer
        x = self.transformer(x)
        
        # Global average pooling + output
        x = self.norm(x).mean(dim=1)
        return self.head(x)


class SpatialViT36x36(nn.Module):
    """Standard Vision Transformer for 36x36 spatial data"""
    
    def __init__(self):
        super().__init__()
        
        # Patch embedding - use 6x6 patches for 36x36 input
        self.patch_embed = nn.Conv2d(1, 256, kernel_size=6, stride=6)  # 36x36 -> 6x6 patches
        self.pos_embed = nn.Parameter(torch.randn(1, 36, 256))
        
        # Transformer blocks
        self.transformer = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(
                d_model=256,
                nhead=8,
                dim_feedforward=512,
                dropout=0.1,
                batch_first=True
            ),
            num_layers=4
        )
        
        # Output
        self.norm = nn.LayerNorm(256)
        self.head = nn.Linear(256, 256)
    
    def forward(self, x):
        # Patch embedding
        x = self.patch_embed(x)  # [batch, 256, 6, 6]
        x = x.flatten(2).transpose(1, 2)  # [batch, 36, 256]
        
        # Add position embedding
        x = x + self.pos_embed
        
        # Transformer
        x = self.transformer(x)
        
        # Global average pooling + output
        x = self.norm(x).mean(dim=1)
        return self.head(x)


# Factory function
def create_spatial_multimodal_model(architecture: str, clinical_feature_dim: int = 12, 
                                   config: Dict[str, Any] = None) -> nn.Module:
    """
    Create spatial-aware multimodal model
    
    Args:
        architecture: Model architecture ('cnn_gru', 'unet', 'vit')
        clinical_feature_dim: Dimension of clinical features
        config: Model configuration
        
    Returns:
        Spatial-aware multimodal model
    """
    
    if architecture.lower() in ['cnn_gru', 'multimodal_cnn_gru']:
        return SpatialMultiModalCNNGRU(clinical_feature_dim, config)
    elif architecture.lower() in ['unet', 'multimodal_unet']:
        return SpatialMultiModalUNet(clinical_feature_dim, config)
    elif architecture.lower() in ['vit', 'multimodal_vit']:
        return SpatialMultiModalViT(clinical_feature_dim, config)
    else:
        raise ValueError(f"Unknown spatial architecture: {architecture}")