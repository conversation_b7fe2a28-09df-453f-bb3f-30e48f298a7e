import os
import time
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWrite<PERSON>
from typing import Dict, Any, Tuple, Optional, List
import logging
from tqdm import tqdm
import numpy as np
from .risk_classification_loss import create_loss_function
from ..evaluation.metrics import MetricsCalculator
from ..utils.config import Config

logger = logging.getLogger(__name__)


class EarlyStopping:
    """Early stopping utility."""
    
    def __init__(self, patience: int = 10, min_delta: float = 1e-4, 
                 mode: str = 'min', restore_best_weights: bool = True):
        """
        Initialize early stopping.
        
        Args:
            patience: Number of epochs to wait for improvement
            min_delta: Minimum change to qualify as improvement
            mode: 'min' for loss, 'max' for accuracy
            restore_best_weights: Whether to restore best weights
        """
        self.patience = patience
        self.min_delta = min_delta
        self.mode = mode
        self.restore_best_weights = restore_best_weights
        
        self.best_score = None
        self.counter = 0
        self.best_weights = None
        self.early_stop = False
        
        if mode == 'min':
            self.is_better = lambda score, best: score < best - float(min_delta)
        else:
            self.is_better = lambda score, best: score > best + float(min_delta)
    
    def __call__(self, score: float, model: nn.Module) -> bool:
        """
        Check if early stopping should be triggered.
        
        Args:
            score: Current validation score
            model: Model to potentially save weights
            
        Returns:
            True if early stopping should be triggered
        """
        if self.best_score is None:
            self.best_score = float(score)
            self.save_checkpoint(model)
        elif self.is_better(float(score), float(self.best_score)):
            self.best_score = float(score)
            self.counter = 0
            self.save_checkpoint(model)
        else:
            self.counter += 1
            
        if self.counter >= self.patience:
            self.early_stop = True
            if self.restore_best_weights and self.best_weights is not None:
                logger.info("Restoring best weights...")
                model.load_state_dict(self.best_weights)
        
        return self.early_stop
    
    def save_checkpoint(self, model: nn.Module):
        """Save model checkpoint."""
        if self.restore_best_weights:
            self.best_weights = model.state_dict().copy()


class LearningRateScheduler:
    """Learning rate scheduler wrapper."""
    
    def __init__(self, optimizer: torch.optim.Optimizer, config: Dict[str, Any]):
        """
        Initialize learning rate scheduler.
        
        Args:
            optimizer: PyTorch optimizer
            config: Scheduler configuration
        """
        self.optimizer = optimizer
        self.config = config
        
        scheduler_type = config.get('type', 'cosine')
        
        if scheduler_type == 'cosine':
            self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer, 
                T_max=int(config.get('T_max', 100)),
                eta_min=float(config.get('min_lr', 1e-6))
            )
        elif scheduler_type == 'step':
            self.scheduler = torch.optim.lr_scheduler.StepLR(
                optimizer,
                step_size=int(config.get('step_size', 30)),
                gamma=float(config.get('gamma', 0.1))
            )
        elif scheduler_type == 'plateau':
            self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                optimizer,
                mode='min',
                factor=float(config.get('factor', 0.5)),
                patience=int(config.get('patience', 10)),
                min_lr=float(config.get('min_lr', 1e-6))
            )
        else:
            self.scheduler = None
        
        # Warmup settings
        self.warmup_epochs = int(config.get('warmup_epochs', 0))
        self.warmup_lr = float(config.get('warmup_lr', 1e-6))
        self.base_lr = optimizer.param_groups[0]['lr']
        self.current_epoch = 0
        
    def step(self, epoch: int, val_loss: Optional[float] = None):
        """Step the scheduler."""
        self.current_epoch = epoch
        
        if epoch < self.warmup_epochs:
            # Warmup phase
            lr = self.warmup_lr + (self.base_lr - self.warmup_lr) * epoch / self.warmup_epochs
            for param_group in self.optimizer.param_groups:
                param_group['lr'] = lr
        else:
            # Regular scheduling
            if self.scheduler is not None:
                if isinstance(self.scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                    if val_loss is not None:
                        self.scheduler.step(val_loss)
                else:
                    self.scheduler.step()
    
    def get_lr(self) -> float:
        """Get current learning rate."""
        return self.optimizer.param_groups[0]['lr']


class MCGTrainer:
    """Main trainer class for MCG model."""
    
    def __init__(self, model: nn.Module, config: Config, device: torch.device,
                 checkpoint_dir: str, tensorboard_dir: str):
        """
        Initialize trainer.
        
        Args:
            model: MCG model
            config: Training configuration
            device: Training device
            checkpoint_dir: Directory for saving checkpoints
            tensorboard_dir: Directory for TensorBoard logs
        """
        self.model = model
        self.config = config
        self.device = device
        self.checkpoint_dir = checkpoint_dir
        self.tensorboard_dir = tensorboard_dir
        
        # Create directories
        os.makedirs(checkpoint_dir, exist_ok=True)
        os.makedirs(tensorboard_dir, exist_ok=True)
        
        # Initialize components
        self.loss_fn = create_loss_function(config.training.__dict__)
        self.optimizer = self._create_optimizer()
        self.scheduler = LearningRateScheduler(self.optimizer, config.training.scheduler)
        self.early_stopping = self._create_early_stopping()
        self.metrics_calculator = MetricsCalculator(config.evaluation.metrics)
        
        # TensorBoard writer
        self.writer = SummaryWriter(log_dir=tensorboard_dir)
        
        # Training state
        self.current_epoch = 0
        self.best_val_loss = float('inf')
        self.train_losses = []
        self.val_losses = []
        
        logger.info(f"MCGTrainer initialized with device: {device}")
        logger.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
    def _create_optimizer(self) -> torch.optim.Optimizer:
        """Create optimizer from configuration."""
        optimizer_config = self.config.training.optimizer
        optimizer_type = optimizer_config.get('type', 'AdamW')
        
        if optimizer_type == 'Adam':
            optimizer = torch.optim.Adam(
                self.model.parameters(),
                lr=float(self.config.training.learning_rate),
                weight_decay=float(self.config.training.weight_decay),
                betas=optimizer_config.get('betas', [0.9, 0.999])
            )
        elif optimizer_type == 'AdamW':
            optimizer = torch.optim.AdamW(
                self.model.parameters(),
                lr=float(self.config.training.learning_rate),
                weight_decay=float(self.config.training.weight_decay),
                betas=optimizer_config.get('betas', [0.9, 0.999])
            )
        elif optimizer_type == 'SGD':
            optimizer = torch.optim.SGD(
                self.model.parameters(),
                lr=float(self.config.training.learning_rate),
                weight_decay=float(self.config.training.weight_decay),
                momentum=optimizer_config.get('momentum', 0.9)
            )
        elif optimizer_type == 'NAdam':
            optimizer = torch.optim.NAdam(
                self.model.parameters(),
                lr=float(self.config.training.learning_rate),
                weight_decay=float(self.config.training.weight_decay),
                betas=optimizer_config.get('betas', [0.9, 0.999]),
                momentum_decay=float(optimizer_config.get('momentum_decay', 4e-3))
            )
        else:
            raise ValueError(f"Unsupported optimizer: {optimizer_type}")
        
        return optimizer
    
    def _create_early_stopping(self) -> Optional[EarlyStopping]:
        """Create early stopping from configuration."""
        early_stopping_config = self.config.training.early_stopping
        
        if early_stopping_config.get('patience', 0) > 0:
            return EarlyStopping(
                patience=early_stopping_config['patience'],
                min_delta=early_stopping_config.get('min_delta', 1e-4),
                mode='min',
                restore_best_weights=True
            )
        return None
    
    def train_epoch(self, train_loader: DataLoader) -> Tuple[Dict[str, float], Dict[str, float]]:
        """Train for one epoch."""
        self.model.train()
        
        total_loss = 0.0
        loss_components = {}
        num_batches = len(train_loader)
        
        # Collect predictions and targets for metrics calculation
        all_predictions = {'risk_scores': [], 'conclusion_probs': []}
        all_targets = {'risk_scores': [], 'conclusion': []}
        
        progress_bar = tqdm(train_loader, desc=f"Epoch {self.current_epoch}")
        
        # Initialize gradients
        self.optimizer.zero_grad()
        
        for batch_idx, batch in enumerate(progress_bar):
            # Move batch to device
            mcg_data = batch['mcg_data'].to(self.device)
            clinical_features = batch['clinical_features'].to(self.device)
            risk_scores = batch['risk_scores'].to(self.device)
            conclusions = batch['conclusion'].to(self.device)
            
            # Forward pass
            predictions = self.model(mcg_data, clinical_features)
            
            # Calculate loss
            targets = {
                'risk_scores': risk_scores,
                'conclusion': conclusions
            }
            
            loss_dict = self.loss_fn(predictions, targets)
            loss = loss_dict['total_loss']
            
            # Get gradient accumulation steps
            grad_accum_steps = getattr(self.config.training, 'gradient_accumulation_steps', 1)
            
            # Scale loss for gradient accumulation
            if grad_accum_steps > 1:
                loss = loss / grad_accum_steps
            
            # Backward pass
            loss.backward()
            
            # Update optimizer every accumulation_steps
            if (batch_idx + 1) % grad_accum_steps == 0:
                # Gradient clipping
                if hasattr(self.config.training, 'grad_clip_norm'):
                    torch.nn.utils.clip_grad_norm_(
                        self.model.parameters(), 
                        self.config.training.grad_clip_norm
                    )
                
                self.optimizer.step()
                self.optimizer.zero_grad()
            
            # Accumulate losses
            total_loss += loss.item()
            for key, value in loss_dict.items():
                if isinstance(value, torch.Tensor):
                    loss_components[key] = loss_components.get(key, 0) + value.item()
                else:
                    loss_components[key] = loss_components.get(key, 0) + value
            
            # Collect predictions and targets for metrics (detached from computation graph)
            with torch.no_grad():
                all_predictions['risk_scores'].append(predictions['risk_scores'].cpu())
                all_predictions['conclusion_probs'].append(
                    torch.sigmoid(predictions['conclusion_logits']).cpu()
                )
                all_targets['risk_scores'].append(risk_scores.cpu())
                all_targets['conclusion'].append(conclusions.cpu())
            
            # Update progress bar
            progress_bar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'lr': f"{self.scheduler.get_lr():.6f}"
            })
            
            # Log batch metrics to TensorBoard
            global_step = self.current_epoch * num_batches + batch_idx
            self.writer.add_scalar('Train/Batch_Loss', loss.item(), global_step)
            self.writer.add_scalar('Train/Learning_Rate', self.scheduler.get_lr(), global_step)
        
        # Average losses
        avg_loss = total_loss / num_batches
        for key in loss_components:
            loss_components[key] /= num_batches
        
        # Concatenate all predictions and targets for metrics
        all_predictions['risk_scores'] = torch.cat(all_predictions['risk_scores'], dim=0)
        all_predictions['conclusion_probs'] = torch.cat(all_predictions['conclusion_probs'], dim=0)
        all_targets['risk_scores'] = torch.cat(all_targets['risk_scores'], dim=0)
        all_targets['conclusion'] = torch.cat(all_targets['conclusion'], dim=0)
        
        # Calculate training metrics
        train_metrics = self.metrics_calculator.calculate_metrics(all_predictions, all_targets)
        
        return {'total_loss': avg_loss, **loss_components}, train_metrics
    
    def validate_epoch(self, val_loader: DataLoader) -> Tuple[Dict[str, float], Dict[str, float]]:
        """Validate for one epoch."""
        self.model.eval()
        
        total_loss = 0.0
        loss_components = {}
        all_predictions = {'risk_scores': [], 'conclusion_probs': []}
        all_targets = {'risk_scores': [], 'conclusion': []}
        
        with torch.no_grad():
            progress_bar = tqdm(val_loader, desc="Validation")
            
            for batch in progress_bar:
                # Move batch to device
                mcg_data = batch['mcg_data'].to(self.device)
                clinical_features = batch['clinical_features'].to(self.device)
                risk_scores = batch['risk_scores'].to(self.device)
                conclusions = batch['conclusion'].to(self.device)
                
                # Forward pass
                predictions = self.model(mcg_data, clinical_features)
                
                # Calculate loss
                targets = {
                    'risk_scores': risk_scores,
                    'conclusion': conclusions
                }
                
                loss_dict = self.loss_fn(predictions, targets)
                loss = loss_dict['total_loss']
                
                # Accumulate losses
                total_loss += loss.item()
                for key, value in loss_dict.items():
                    if isinstance(value, torch.Tensor):
                        loss_components[key] = loss_components.get(key, 0) + value.item()
                    else:
                        loss_components[key] = loss_components.get(key, 0) + value
                
                # Collect predictions and targets for metrics
                all_predictions['risk_scores'].append(predictions['risk_scores'].cpu())
                all_predictions['conclusion_probs'].append(
                    torch.sigmoid(predictions['conclusion_logits']).cpu()
                )
                all_targets['risk_scores'].append(risk_scores.cpu())
                all_targets['conclusion'].append(conclusions.cpu())
        
        # Average losses
        num_batches = len(val_loader)
        avg_loss = total_loss / num_batches
        for key in loss_components:
            loss_components[key] /= num_batches
        
        # Concatenate all predictions and targets
        all_predictions['risk_scores'] = torch.cat(all_predictions['risk_scores'], dim=0)
        all_predictions['conclusion_probs'] = torch.cat(all_predictions['conclusion_probs'], dim=0)
        all_targets['risk_scores'] = torch.cat(all_targets['risk_scores'], dim=0)
        all_targets['conclusion'] = torch.cat(all_targets['conclusion'], dim=0)
        
        # Calculate metrics
        metrics = self.metrics_calculator.calculate_metrics(all_predictions, all_targets)
        
        return {'total_loss': avg_loss, **loss_components}, metrics
    
    def train(self, train_loader: DataLoader, val_loader: DataLoader) -> Dict[str, List[float]]:
        """Main training loop."""
        logger.info(f"Starting training for {self.config.training.num_epochs} epochs")
        
        training_history = {
            'train_loss': [],
            'val_loss': [],
            'learning_rate': [],
            'epoch_time': []
        }
        
        for epoch in range(self.config.training.num_epochs):
            self.current_epoch = epoch
            epoch_start_time = time.time()
            
            # Training
            train_metrics, train_eval_metrics = self.train_epoch(train_loader)
            
            # Validation
            val_metrics, val_eval_metrics = self.validate_epoch(val_loader)
            
            # Update learning rate
            self.scheduler.step(epoch, val_metrics['total_loss'])
            
            # Record history
            training_history['train_loss'].append(train_metrics['total_loss'])
            training_history['val_loss'].append(val_metrics['total_loss'])
            training_history['learning_rate'].append(self.scheduler.get_lr())
            training_history['epoch_time'].append(time.time() - epoch_start_time)
            
            # Log to TensorBoard
            self._log_epoch_metrics(train_metrics, val_metrics, train_eval_metrics, val_eval_metrics)
            
            # Print epoch summary with key metrics
            train_acc = train_eval_metrics.get('conclusion_accuracy', 0.0)
            train_auc = train_eval_metrics.get('conclusion_auc', 0.0)
            val_acc = val_eval_metrics.get('conclusion_accuracy', 0.0)
            val_auc = val_eval_metrics.get('conclusion_auc', 0.0)
            
            logger.info(
                f"Epoch {epoch+1}/{self.config.training.num_epochs} - "
                f"Train Loss: {train_metrics['total_loss']:.4f}, "
                f"Train ACC: {train_acc:.4f}, "
                f"Train AUC: {train_auc:.4f}, "
                f"Val Loss: {val_metrics['total_loss']:.4f}, "
                f"Val ACC: {val_acc:.4f}, "
                f"Val AUC: {val_auc:.4f}, "
                f"LR: {self.scheduler.get_lr():.6f}, "
                f"Time: {training_history['epoch_time'][-1]:.1f}s"
            )
            
            # Save checkpoint
            if (epoch + 1) % self.config.training.checkpoint.get('save_every_n_epochs', 5) == 0:
                self._save_checkpoint(epoch, val_metrics['total_loss'])
            
            # Early stopping
            if self.early_stopping is not None:
                if self.early_stopping(val_metrics['total_loss'], self.model):
                    logger.info(f"Early stopping triggered at epoch {epoch+1}")
                    break
            
            # Update best validation loss
            if val_metrics['total_loss'] < self.best_val_loss:
                self.best_val_loss = val_metrics['total_loss']
                if self.config.training.checkpoint.get('keep_best_only', True):
                    self._save_checkpoint(epoch, val_metrics['total_loss'], is_best=True)
        
        logger.info("Training completed!")
        self.writer.close()
        
        return training_history
    
    def _log_epoch_metrics(self, train_metrics: Dict[str, float], 
                          val_metrics: Dict[str, float], 
                          train_eval_metrics: Dict[str, float],
                          val_eval_metrics: Dict[str, float]):
        """Log metrics to TensorBoard."""
        epoch = self.current_epoch
        
        # Loss metrics
        self.writer.add_scalars('Loss/Total', {
            'Train': train_metrics['total_loss'],
            'Validation': val_metrics['total_loss']
        }, epoch)
        
        if 'risk_loss' in train_metrics:
            self.writer.add_scalars('Loss/Risk', {
                'Train': train_metrics['risk_loss'],
                'Validation': val_metrics['risk_loss']
            }, epoch)
        
        if 'conclusion_loss' in train_metrics:
            self.writer.add_scalars('Loss/Conclusion', {
                'Train': train_metrics['conclusion_loss'],
                'Validation': val_metrics['conclusion_loss']
            }, epoch)
        
        # Classification metrics for conclusion
        conclusion_metrics = ['conclusion_accuracy', 'conclusion_auc', 'conclusion_sensitivity', 'conclusion_specificity']
        for metric_name in conclusion_metrics:
            if metric_name in train_eval_metrics and metric_name in val_eval_metrics:
                clean_name = metric_name.replace('conclusion_', '').upper()
                self.writer.add_scalars(f'Metrics/{clean_name}', {
                    'Train': train_eval_metrics[metric_name],
                    'Validation': val_eval_metrics[metric_name]
                }, epoch)
        
        # Risk regression metrics
        risk_metrics = ['risk_mse', 'risk_mae', 'risk_rmse']
        for metric_name in risk_metrics:
            if metric_name in train_eval_metrics and metric_name in val_eval_metrics:
                clean_name = metric_name.replace('risk_', '').upper()
                self.writer.add_scalars(f'Risk_Metrics/{clean_name}', {
                    'Train': train_eval_metrics[metric_name],
                    'Validation': val_eval_metrics[metric_name]
                }, epoch)
        
        # Learning rate
        self.writer.add_scalar('Training/Learning_Rate', self.scheduler.get_lr(), epoch)
        
        # Model weights histogram
        if epoch % 10 == 0:  # Log every 10 epochs to avoid too much data
            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    try:
                        self.writer.add_histogram(f'Weights/{name}', param.data, epoch)
                        self.writer.add_histogram(f'Gradients/{name}', param.grad.data, epoch)
                    except Exception as e:
                        # Skip histogram logging if there's a numpy compatibility issue
                        logger.debug(f"Skipped histogram logging for {name}: {e}")
    
    def _save_checkpoint(self, epoch: int, val_loss: float, is_best: bool = False):
        """Save model checkpoint."""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.scheduler.state_dict() if self.scheduler.scheduler else None,
            'val_loss': val_loss,
            'config': self.config
        }
        
        if is_best:
            checkpoint_path = os.path.join(self.checkpoint_dir, 'best_model.pth')
        else:
            checkpoint_path = os.path.join(self.checkpoint_dir, f'checkpoint_epoch_{epoch+1}.pth')
        
        torch.save(checkpoint, checkpoint_path)
        logger.info(f"Checkpoint saved: {checkpoint_path}")
    
    def load_checkpoint(self, checkpoint_path: str) -> int:
        """
        Load model checkpoint.
        
        Args:
            checkpoint_path: Path to checkpoint file
            
        Returns:
            Last epoch number
        """
        logger.info(f"Loading checkpoint: {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        if checkpoint['scheduler_state_dict'] and self.scheduler.scheduler:
            self.scheduler.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        self.current_epoch = checkpoint['epoch']
        self.best_val_loss = checkpoint['val_loss']
        
        logger.info(f"Checkpoint loaded. Resuming from epoch {self.current_epoch + 1}")
        return self.current_epoch