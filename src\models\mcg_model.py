"""
统一的42通道多模态MCG模型工厂
整合所有最优化模型：CNN+GRU、UNet、ViT
"""

import torch
import torch.nn as nn
from typing import Dict, Any, Union
import logging

from .multimodal_cnn_gru import create_multimodal_cnn_gru_model
from .multimodal_unet import create_multimodal_unet_model
from .multimodal_vit import create_multimodal_vit_model
from .spatial_multimodal_models import create_spatial_multimodal_model

logger = logging.getLogger(__name__)


# 支持的42通道多模态模型
SUPPORTED_MODELS = {
    'multimodal_cnn_gru': {
        'creator': create_multimodal_cnn_gru_model,
        'description': '42-channel CNN+GRU with dual-branch processing and cross-modal attention'
    },
    'multimodal_unet': {
        'creator': create_multimodal_unet_model,
        'description': '42-channel UNet with encoder-decoder architecture and multi-scale fusion'
    },
    'multimodal_vit': {
        'creator': create_multimodal_vit_model,
        'description': '42-channel Vision Transformer with cross-modal transformer fusion'
    }
}


def create_mcg_model(model_config: Union[Dict[str, Any], object], 
                     clinical_feature_dim: int = 0) -> nn.Module:
    """
    创建42通道多模态MCG模型
    
    Args:
        model_config: 模型配置 (dict 或 config object)
        clinical_feature_dim: 临床特征维度
    
    Returns:
        创建的模型实例
    """
    # 处理配置对象
    if hasattr(model_config, '__dict__'):
        # 如果是配置对象，转换为字典
        config_dict = {}
        for key, value in model_config.__dict__.items():
            if not key.startswith('_'):
                config_dict[key] = value
        model_config = config_dict
    
    # 获取模型类型 (支持 'model_type' 或 'architecture' 字段)
    model_type = model_config.get('model_type') or model_config.get('architecture', 'multimodal_cnn_gru')
    
    # 检查是否启用空间感知处理
    spatial_config = model_config.get('spatial', {})
    spatial_enabled = spatial_config.get('enabled', False)
    
    if spatial_enabled:
        # 使用空间感知模型
        logger.info(f"Creating spatial-aware {model_type} model")
        model = create_spatial_multimodal_model(model_type, clinical_feature_dim, model_config)
    else:
        # 使用传统42通道模型
        if model_type not in SUPPORTED_MODELS:
            available_models = ', '.join(SUPPORTED_MODELS.keys())
            raise ValueError(f"Unsupported model type: {model_type}. Available: {available_models}")
        
        model_creator = SUPPORTED_MODELS[model_type]['creator']
        model = model_creator(model_config, clinical_feature_dim)
    
    # 日志记录
    model_info = model.get_model_info()
    logger.info(f"Created {model_info['architecture']}: {model_info['total_parameters']:,} parameters")
    
    return model


def get_available_models() -> Dict[str, str]:
    """获取可用的模型类型及描述"""
    return {model_type: info['description'] for model_type, info in SUPPORTED_MODELS.items()}


def get_default_config(model_type: str = 'multimodal_cnn_gru') -> Dict[str, Any]:
    """获取指定模型的默认配置"""
    
    if model_type == 'multimodal_cnn_gru':
        return {
            'model_type': 'multimodal_cnn_gru',
            'signal_cnn': {
                'base_channels': 64,
                'num_blocks': 4,
                'dropout': 0.2
            },
            'timepoint': {
                'hidden_dim': 128,
                'num_layers': 3,
                'dropout': 0.2
            },
            'gru': {
                'hidden_dim': 512,
                'num_layers': 3,
                'dropout': 0.3
            },
            'fusion': {
                'num_heads': 8,
                'dropout': 0.1
            },
            'clinical': {
                'hidden_dim': 128,
                'dropout': 0.3
            },
            'final_fusion': {
                'hidden_dim': 512,
                'dropout': 0.3
            },
            'output': {
                'risk_head_hidden': 256,
                'conclusion_head_hidden': 128,
                'dropout': 0.4
            }
        }
    
    elif model_type == 'multimodal_unet':
        return {
            'model_type': 'multimodal_unet',
            'signal_unet': {
                'base_channels': 64,
                'num_blocks': 4,
                'dropout': 0.2
            },
            'timepoint_unet': {
                'hidden_dim': 128,
                'num_layers': 3,
                'dropout': 0.2
            },
            'fusion': {
                'dropout': 0.1
            },
            'clinical': {
                'hidden_dim': 128,
                'dropout': 0.3
            },
            'final_fusion': {
                'hidden_dim': 512,
                'dropout': 0.3
            },
            'output': {
                'risk_head_hidden': 256,
                'conclusion_head_hidden': 128,
                'dropout': 0.4
            }
        }
    
    elif model_type == 'multimodal_vit':
        return {
            'model_type': 'multimodal_vit',
            'signal_vit': {
                'embed_dim': 768,
                'num_layers': 12,
                'num_heads': 12,
                'patch_size': 16,
                'dropout': 0.1,
                'drop_path_rate': 0.1
            },
            'timepoint_vit': {
                'embed_dim': 384,
                'num_layers': 6,
                'num_heads': 6,
                'patch_size': 32,
                'dropout': 0.1
            },
            'fusion': {
                'embed_dim': 512,
                'num_heads': 8,
                'dropout': 0.1
            },
            'clinical': {
                'hidden_dim': 256,
                'dropout': 0.3
            },
            'final_fusion': {
                'hidden_dim': 1024,
                'dropout': 0.3
            },
            'output': {
                'risk_head_hidden': 512,
                'conclusion_head_hidden': 256,
                'dropout': 0.4
            }
        }
    
    else:
        raise ValueError(f"Unknown model type: {model_type}")


def create_model_from_type(model_type: str, clinical_feature_dim: int = 0) -> nn.Module:
    """使用默认配置创建指定类型的模型"""
    config = get_default_config(model_type)
    return create_mcg_model(config, clinical_feature_dim)


def print_model_comparison():
    """打印模型对比信息"""
    print("\n" + "="*80)
    print("42通道多模态MCG模型对比")
    print("="*80)
    
    for model_type, info in SUPPORTED_MODELS.items():
        print(f"\n{model_type.upper()}:")
        print(f"  描述: {info['description']}")
        
        # 创建示例模型获取参数量
        try:
            model = create_model_from_type(model_type, clinical_feature_dim=12)
            model_info = model.get_model_info()
            print(f"  参数量: {model_info['total_parameters']:,}")
            print(f"  优化特性: {', '.join(model_info['optimization_features'])}")
        except Exception as e:
            print(f"  参数量: 无法计算 ({e})")
    
    print("\n" + "="*80)


if __name__ == "__main__":
    # 测试模型创建
    print_model_comparison()