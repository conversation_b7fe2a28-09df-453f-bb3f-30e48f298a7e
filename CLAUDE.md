### **深度学习项目核心任务说明书 (Core Task Description)**

#### **1. 项目总览 (Project Overview)**

本项目的核心目标是基于给定的心磁图 (MCG) 时空数据和患者临床特征，利用 PyTorch 从零开始构建、训练和评估一个深度学习模型。该模型需要解决一个多任务学习问题，即同时预测四项经 Sigmoid 函数变换后的血管狭窄风险指标和一项衍生的二分类诊断结论。整个开发过程需覆盖数据处理、模型训练、评估和保存的完整生命周期。

#### **2. 数据源与格式详解 (Detailed Data Source & Format)**

开发所需的所有数据和信息均由指定的 `.txt` 文件和 `.xlsx` 文件提供。

##### **2.1 心磁时空数据 (MCG Spatiotemporal Data)**

- **文件格式**: 原始信号数据以独立的 `.txt` 文件存储。
    
- **命名规则**: 每个文件名与样本ID（即'心磁号'）对应，格式为 `{id}.txt`。
    
- **数据结构**: 每个 `.txt` 文件包含一个 `n_timestamp * 37` 的矩阵（制表符分隔）。
    
    - **重要**: 第一列为索引列，需要忽略。剩余的 `36` 个通道具有空间关联性，重新排列后形成 `6x6` 的正方形网格。
        
    - `n_timestamp` 代表时间序列的长度，该长度在不同样本间可能不一致。
        
- **数据规模**: 约有 3000 份样本。
    

##### **2.2 标签、ID 与数据集划分 (Labels, IDs, and Dataset Splits)**

此部分信息全部包含在指定的 `.xlsx` 电子表格文件中。

- **文件结构**:
    
    - **Sheet '训练集'**: 包含用于模型训练的所有样本ID ('心磁号') 及部分标签和临床特征。
        
    - **Sheet '内测集'**: 包含用于模型评估的所有样本ID ('心磁号') 及部分标签和临床特征。
        
    - **Sheet '训测集三支信息'**: 包含额外的标签信息，需要通过'心磁号'进行匹配。
        
- **ID 关联**: '心磁号' (string 类型) 是唯一标识符，用于关联 `.txt` 信号数据、临床特征和所有标签。
    

##### **2.3 五个预测标签 (The Five Prediction Targets)**

这是一个多任务学习问题，模型需要同时输出五个标签的预测结果。

1. **血管狭窄风险指标 (变换后的回归任务)**:
    
    - **来源**: 位于 '训测集三支信息' sheet 中的 `LM`, `LAD`, `LCX`, `RCA` 列。
        
    - **标签变换**: 原始的 `0-100` 的狭窄程度值**不是**模型的直接预测目标。需要先通过以下公式进行变换，得到 `(0, 1)` 区间内的风险指标，作为模型的学习目标：
        
        risk_score=sigmoid(10狭窄程度−50​)
        
    - **模型输出**: 模型需要为这四个血管分别输出一个 `(0, 1)` 之间的预测值。
        
2. **造影结论 (二分类任务)**:
    
    - **来源**: 直接位于 '训练集' 和 '内测集' sheet 中。
        
    - **标签列名**: '造影结论'。
        
    - **取值**: 0 或 1。
        
    - **派生规则**: 该标签的生成逻辑是基于**原始狭窄程度**：**只要 `LM`, `LAD`, `LCX`, `RCA` 四个原始值中任意一个超过 70，则'造影结论'为 1，否则为 0**。
        

##### **2.4 临床特征 (Clinical Features)**

临床特征是提升模型性能的关键信息，需要与心磁数据一同输入模型。

- **来源**: 位于 '训练集' 和 '内测集' sheet 中，与样本ID一一对应。
    
- **特征列表**:
    
    - '临床特征-身高' (数值)
        
    - '临床特征-体重' (数值)
        
    - '临床特征-性别' (文本: '男' 或 '女')
        
    - '临床特征-年龄' (数值)
        
    - '临床特征-吸烟' (数值, 通常为0/1)
        
    - '临床特征-饮酒' (数值, 通常为0/1)
        
    - '临床特征-高血压' (数值, 通常为0/1)
        
    - '临床特征-糖尿病' (数值, 通常为0/1)
        
    - '临床特征-高脂血症' (数值, 通常为0/1)
        
    - '临床特征-典型症状' (数值, 通常为0/1)
        
    - '临床特征-既往介入' (数值, 通常为0/1)
        
    - '临床特征-所在医院' (文本)
        
- **重要提示**: **所有临床特征都可能存在空值 (NaN)**，必须在预处理阶段进行妥善处理。
    

#### **3. 核心开发任务与要求 (Core Development Tasks & Requirements)**

##### **3.1 数据预处理 (Data Preprocessing)**

- **处理时序数据**:
    
    - 需解决 `n_timestamp` 长度不一的问题。建议的策略是**填充 (padding)** 到统一长度（大部分在 500-1500，最大不超过 2000）。
        
    - 数据的空间关系（6x6网格）和正负值关系必须被保留。
        
    - 需实现可配置的数据归一化策略。**重要**: 现使用**样本级归一化**（每个样本独立归一化），支持标准归一化、MaxAbs归一化和MinMax归一化。
        
- **处理临床特征**:
    
    - 必须对所有临床特征的**空值**进行处理。
        
    - 必须对**文本型特征**（如'性别', '所在医院'）进行数值化编码。
        
- **数据增强 (可选)**:
    
    - 为提升模型鲁棒性，可选择性地实现数据增强方法。
        
    - 建议考虑的策略包括：`SMOTE` 或 `mixup`（尤其针对分类任务），以及对信号通道**增加不稳定的中高频 (20-50Hz) 正弦噪声**。
        

##### **3.2 模型构建 (Model Construction)**

- **核心架构要求**:
    
    - 模型必须能有效处理心磁数据的**时空特性**（时间序列性 和 通道空间关系）。
        
    - 架构设计需要具备良好的**模块化和可扩展性**。
        
- **架构探索方向**:
    
    - 可以尝试多种有前景的架构，例如：
        
        1. **CNN+RNN 组合**: 使用 CNN 提取空间特征，后接 GRU 或 LSTM 捕捉时间序列依赖。
            
        2. **类 U-Net 架构**: 借鉴图像分割模型的思路进行特征提取和融合。
            
        3. **Transformer-based 架构**: 如 Vision Transformer (ViT) 的变体，用于端到端地学习时空表征。
            
- **特征融合**:
    
    - 模型必须在**恰当的阶段**以**恰当的形式**嵌入临床特征，以提升整体性能。
        

##### **3.3 模型训练 (Model Training)**

- **训练流程**:
    
    - 需基于 PyTorch 从零构建完整的训练和验证循环 (training/validation loop)。
        
- **可配置性**:
    
    - 训练过程中的关键组件必须是可配置的，包括但不限于：**优化器 (Optimizer)**、**学习率预热 (Warmup) 策略**、**损失函数 (Loss Function)**。
        
- **损失函数设计**:
    
    - 这是一个关键任务。损失函数需要支持多任务学习，即同时处理变换后的回归任务和分类任务。
        
    - 应考虑为不同任务的损失（风险指标损失和分类结论损失）设置**可配置的权重**。
        
    - 必须考虑或实现类似 **Focal Loss** 的设计思路，以处理潜在的样本不平衡问题。
        
- **训练监控**:
    
    - 必须集成 **TensorBoard**，用于可视化和管理训练过程中的各项指标。
        

##### **3.4 模型评估 (Model Evaluation)**

- **评估指标**:
    
    - 需要使用多种指标进行全面的模型评估。
        
    - 除了常规指标，必须包含**敏感度 (Sensitivity)** 和 **特异度 (Specificity)**。
        
- **结果可视化**:
    
    - 评估结果应在 TensorBoard 中进行可视化展示。

#### **4. 已解决的关键问题 (Resolved Critical Issues)**

##### **4.1 数据读取问题**
- **问题**: MCG数据格式理解错误，原以为是(36, n_timestamp)格式
- **解决**: 正确实现(n_timestamp, 37)格式读取，忽略第一列索引，使用pandas读取制表符分隔数据

##### **4.2 配置访问错误**  
- **问题**: 代码中使用字典样式访问DataConfig对象导致错误
- **解决**: 统一修改为属性访问模式(`config.data.xlsx_file`)

##### **4.3 预处理维度不匹配**
- **问题**: 跨样本归一化导致不同序列长度样本处理失败
- **解决**: 改为样本级归一化，每个样本独立处理，避免维度冲突

##### **4.4 标签分布问题**
- **问题**: 样本加载失败时总是回退到标签0，导致单类别问题
- **解决**: 修改回退逻辑保留真实标签，确保训练时有两个类别

##### **4.5 早停机制类型错误**
- **问题**: early stopping中数值类型转换错误
- **解决**: 添加显式float()类型转换确保数值比较正确

##### **4.6 训练稳定性**
- **解决后状态**: 
  - 训练loss正常下降(0.1657 → 0.1225 → 0.1195...)
  - 验证指标稳定计算
  - 早停机制正常工作
  - TensorBoard日志正常记录
  - 模型参数约80万个，大小3.1MB

