"""
多模态MCG数据加载器 - 42通道双分支架构
根据专家建议：36信号通道 + 6独立时刻点通道
"""

import numpy as np
import pandas as pd
from typing import Dict, Tuple, Optional, List
import logging

logger = logging.getLogger(__name__)


def create_separate_timepoint_channels(timepoints: Dict[str, int], sequence_length: int, 
                                      gaussian_sigma: float = 20.0) -> np.ndarray:
    """
    为每个时刻点创建独立的高斯核通道，避免信息混淆
    
    Args:
        timepoints: 时刻点字典，例如 {'Qp': 383, 'Rp': 416, 'Sp': 445, 'To': 654, 'Tp': 704, 'Te': 742}
        sequence_length: 序列长度
        gaussian_sigma: 高斯核标准差
        
    Returns:
        shape为(6, sequence_length)的时刻点通道数组，每个通道对应一个时刻点
    """
    # 定义6个标准时刻点的顺序
    timepoint_names = ['Qp', 'Rp', 'Sp', 'To', 'Tp', 'Te']
    
    # 初始化6个独立通道
    timepoint_channels = np.zeros((6, sequence_length), dtype=np.float32)
    
    for i, tp_name in enumerate(timepoint_names):
        if tp_name in timepoints:
            position = timepoints[tp_name]
            
            # 跳过无效位置
            if pd.isna(position) or position < 0 or position >= sequence_length:
                logger.debug(f"Skipping invalid timepoint {tp_name}: {position}")
                continue
                
            # 为当前时刻点创建高斯峰
            x = np.arange(sequence_length, dtype=np.float32)
            gaussian_peak = np.exp(-0.5 * ((x - position) / gaussian_sigma) ** 2)
            
            # 赋值给对应的独立通道
            timepoint_channels[i] = gaussian_peak
            
            logger.debug(f"Created timepoint channel {i} for {tp_name} at position {position}")
    
    return timepoint_channels


def extract_multimodal_features(mcg_data: np.ndarray, timepoint_info: Dict[str, int], 
                               gaussian_config: dict = None, 
                               normalize_signals: bool = True) -> np.ndarray:
    """
    提取42通道多模态特征：先归一化36信号通道，再加入高斯平滑的6独立时刻点通道
    
    数据流程：原始信号 → 归一化 → 高斯平滑时刻点通道 → 组合42通道
    
    Args:
        mcg_data: 原始MCG数据 shape (6, 6, n_timestamp)
        timepoint_info: 时刻点位置信息字典
        gaussian_config: 高斯平滑配置
        normalize_signals: 是否归一化信号通道
        
    Returns:
        增强MCG数据 shape (42, n_timestamp): 前36通道为归一化信号，后6通道为高斯平滑时刻点
    """
    # 默认高斯配置
    if gaussian_config is None:
        gaussian_config = {'sigma': 20.0, 'enabled': True}
    
    sequence_length = mcg_data.shape[2]
    
    # 步骤1: 提取36个信号通道 (6, 6, n_timestamp) → (36, n_timestamp)
    signal_channels = mcg_data.reshape(36, sequence_length).astype(np.float32)
    
    # 步骤2: 归一化信号通道 (样本级标准归一化)
    if normalize_signals:
        # 使用样本级Z-score归一化，确保每个样本独立处理
        # 计算36个信号通道的整体统计量
        all_signal_data = signal_channels.flatten()
        mean_val = np.mean(all_signal_data)
        std_val = np.std(all_signal_data)
        
        if std_val > 1e-8:  # 避免除零
            signal_channels = (signal_channels - mean_val) / std_val
        else:
            signal_channels = signal_channels - mean_val
                
        logger.debug(f"Normalized 36 signal channels using sample-level Z-score normalization")
    
    # 步骤3: 创建6个独立的高斯平滑时刻点通道
    if gaussian_config.get('enabled', True):
        gaussian_sigma = gaussian_config.get('sigma', 20.0)
        timepoint_channels = create_separate_timepoint_channels(
            timepoint_info, sequence_length, gaussian_sigma
        )
        logger.debug(f"Created 6 Gaussian timepoint channels with σ={gaussian_sigma}")
    else:
        # 不使用高斯平滑 - 使用离散脉冲
        timepoint_channels = np.zeros((6, sequence_length), dtype=np.float32)
        timepoint_names = ['Qp', 'Rp', 'Sp', 'To', 'Tp', 'Te']
        
        for i, tp_name in enumerate(timepoint_names):
            if tp_name in timepoint_info:
                position = timepoint_info[tp_name]
                if 0 <= position < sequence_length:
                    timepoint_channels[i, position] = 1.0
        logger.debug(f"Created 6 discrete pulse timepoint channels")
    
    # 步骤4: 组合为42通道数据：前36通道为归一化信号，后6通道为高斯平滑时刻点
    multimodal_data = np.vstack([signal_channels, timepoint_channels])
    
    logger.debug(f"Created 42-channel multimodal data: {multimodal_data.shape} "
                f"(36 normalized signal + 6 Gaussian timepoint channels)")
    
    return multimodal_data


def apply_differential_normalization(multimodal_data: np.ndarray, 
                                    fit_signal_stats: bool = True,
                                    signal_stats: Optional[Tuple] = None) -> Tuple[np.ndarray, Tuple]:
    """
    对信号通道和时刻点通道应用不同的归一化策略
    
    Args:
        multimodal_data: 42通道数据 shape (42, sequence_length)
        fit_signal_stats: 是否拟合信号通道的统计量
        signal_stats: 预先计算的信号通道统计量 (mean, std)
        
    Returns:
        归一化后的数据和信号统计量
    """
    # 分离信号通道和时刻点通道
    signal_channels = multimodal_data[:36]  # 前36通道
    timepoint_channels = multimodal_data[36:]  # 后6通道
    
    # 信号通道归一化：使用标准化 (Z-score normalization)
    if fit_signal_stats:
        # 计算跨通道的统计量（每个样本独立计算）
        signal_mean = np.mean(signal_channels)
        signal_std = np.std(signal_channels)
        signal_std = max(signal_std, 1e-8)  # 防止除零
        signal_stats = (signal_mean, signal_std)
    else:
        signal_mean, signal_std = signal_stats
    
    normalized_signals = (signal_channels - signal_mean) / signal_std
    
    # 时刻点通道归一化：每个通道独立的Min-Max归一化
    normalized_timepoints = np.zeros_like(timepoint_channels)
    for i in range(6):
        channel_data = timepoint_channels[i]
        channel_min = np.min(channel_data)
        channel_max = np.max(channel_data)
        
        if channel_max > channel_min:
            # Min-Max归一化到[0, 1]
            normalized_timepoints[i] = (channel_data - channel_min) / (channel_max - channel_min)
        else:
            # 如果通道全为常数，保持原值
            normalized_timepoints[i] = channel_data
    
    # 重新合并
    normalized_data = np.vstack([normalized_signals, normalized_timepoints])
    
    logger.debug(f"Applied differential normalization: signals Z-score, timepoints min-max")
    
    return normalized_data, signal_stats


class MultimodalMCGDataProcessor:
    """
    42通道多模态MCG数据处理器
    专门处理信号通道与时刻点通道的异构特性
    """
    
    def __init__(self, gaussian_config: dict = None):
        self.gaussian_config = gaussian_config or {'sigma': 20.0, 'enabled': True}
        self.signal_stats = None
        
    def process_sample(self, mcg_data: np.ndarray, timepoint_info: Dict[str, int], 
                      fit_stats: bool = False) -> np.ndarray:
        """
        处理单个样本，提取42通道多模态特征并应用差异化归一化
        
        Args:
            mcg_data: 原始MCG数据 shape (6, 6, n_timestamp)
            timepoint_info: 时刻点信息
            fit_stats: 是否拟合归一化统计量
            
        Returns:
            处理后的42通道数据 shape (42, n_timestamp)
        """
        # 提取42通道多模态特征
        multimodal_data = extract_multimodal_features(
            mcg_data, timepoint_info, self.gaussian_config
        )
        
        # 应用差异化归一化
        normalized_data, signal_stats = apply_differential_normalization(
            multimodal_data, fit_stats, self.signal_stats
        )
        
        # 更新统计量
        if fit_stats:
            self.signal_stats = signal_stats
        
        return normalized_data
    
    def get_channel_info(self) -> Dict:
        """获取通道信息"""
        return {
            'total_channels': 42,
            'signal_channels': list(range(36)),
            'timepoint_channels': list(range(36, 42)),
            'timepoint_names': ['Qp', 'Rp', 'Sp', 'To', 'Tp', 'Te'],
            'normalization': {
                'signals': 'z-score (cross-channel)',
                'timepoints': 'min-max (per-channel)'
            }
        }


def validate_timepoint_info(timepoint_info: Dict[str, int], sequence_length: int) -> Dict[str, int]:
    """
    验证和清理时刻点信息
    
    Args:
        timepoint_info: 时刻点信息字典
        sequence_length: 序列长度
        
    Returns:
        验证后的时刻点信息
    """
    valid_timepoints = {}
    required_timepoints = ['Qp', 'Rp', 'Sp', 'To', 'Tp', 'Te']
    
    for tp_name in required_timepoints:
        if tp_name in timepoint_info:
            position = timepoint_info[tp_name]
            if not pd.isna(position) and 0 <= position < sequence_length:
                valid_timepoints[tp_name] = int(position)
            else:
                logger.warning(f"Invalid timepoint {tp_name}: {position}")
        else:
            logger.warning(f"Missing timepoint: {tp_name}")
    
    logger.info(f"Valid timepoints: {list(valid_timepoints.keys())}")
    return valid_timepoints


# 为了保持向后兼容性，提供一个适配器函数
def extract_timepoint_features_multimodal(mcg_data: np.ndarray, timepoint_info: Dict[str, int], 
                                        gaussian_config: dict = None) -> np.ndarray:
    """
    向后兼容的42通道特征提取函数
    
    这个函数是原有 extract_timepoint_features 函数的升级版本
    从37通道升级到42通道多模态架构
    """
    return extract_multimodal_features(mcg_data, timepoint_info, gaussian_config)