# MCG Deep Learning Project

A complete deep learning pipeline for Magnetocardiography (MCG) data analysis, implementing multi-task learning for cardiovascular risk assessment.

## Project Overview

This project implements a CNN+RNN hybrid deep learning model for analyzing MCG spatiotemporal data combined with clinical features to predict:

1. **Four vessel stenosis risk scores** (LM, LAD, LCX, RCA) - sigmoid-transformed regression targets
2. **Binary diagnostic conclusion** - derived from stenosis severity

## Features

- **Modular Architecture**: Clean, extensible codebase with separate modules for data, models, training, and evaluation
- **Multi-task Learning**: Simultaneous prediction of regression and classification targets with configurable loss weights
- **Advanced Model Architecture**: CNN for spatial feature extraction + RNN for temporal modeling + clinical feature fusion
- **Comprehensive Preprocessing**: Handles variable-length sequences, missing values, and data normalization
- **Training Pipeline**: Full training loop with TensorBoard integration, early stopping, and checkpointing
- **Evaluation Suite**: Multiple metrics including sensitivity, specificity, AUC, and medical-specific assessments
- **Visualization Tools**: Training curves, confusion matrices, ROC curves, and prediction distributions
- **Production Ready**: Model export, checkpointing, and inference scripts

## Project Structure

```
SoftLabel_DL/
├── src/
│   ├── data/
│   │   ├── loader.py          # Data loading utilities
│   │   ├── preprocessing.py   # MCG data preprocessing
│   │   └── dataset.py         # PyTorch Dataset and DataLoader
│   ├── models/
│   │   ├── spatial_extractor.py   # CNN for spatial features
│   │   ├── temporal_model.py      # RNN for temporal modeling  
│   │   └── mcg_model.py           # Complete model architecture
│   ├── training/
│   │   ├── loss.py            # Multi-task loss functions
│   │   └── trainer.py         # Training loop and utilities
│   ├── evaluation/
│   │   ├── metrics.py         # Evaluation metrics
│   │   └── visualization.py   # Plotting and visualization
│   └── utils/
│       ├── config.py          # Configuration management
│       └── checkpoint.py      # Model persistence
├── configs/
│   └── config.yaml           # Main configuration file
├── train.py                  # Main training script
├── inference.py              # Inference script
├── requirements.txt          # Python dependencies
└── README.md                # This file
```

## Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd SoftLabel_DL
```

2. **Create and activate conda environment** (recommended):
```bash
conda create -n torch_13 python=3.8
conda activate torch_13
```

3. **Install dependencies**:
```bash
pip install -r requirements.txt
```

## Data Format

### Input Data Requirements

1. **MCG Signal Files** (`.txt` format):
   - Each file named as `{mcg_id}.txt`
   - Contains n_timestamp×37 matrix (tab-separated)
   - First column is index (ignored), remaining 36 channels form 6×6 spatial grid
   - Variable sequence length, automatically padded/truncated
   - Located in specified directory

2. **Excel File** with three sheets:
   - **训练集**: Training set with MCG IDs, clinical features, and conclusion labels
   - **内测集**: Test set with similar structure
   - **训测集三支信息**: Vessel stenosis information (LM, LAD, LCX, RCA values)

3. **Clinical Features**:
   - Height, weight, gender, age
   - Risk factors: smoking, drinking, hypertension, diabetes, hyperlipidemia
   - Symptoms and medical history
   - Hospital information

### Data Preprocessing

- **Sequence Padding**: Variable-length sequences padded to fixed length (default: 2000)
- **Spatial Preservation**: Maintains 6×6 grid structure from 36 channels
- **Sample-wise Normalization**: Each sample normalized independently (standard, maxabs, or minmax)
- **Clinical Features**: Missing value imputation and categorical encoding
- **Label Transformation**: Stenosis values transformed using sigmoid((x-50)/10)
- **Fallback Handling**: Robust error handling with real label preservation

## Model Architecture

### CNN+RNN Hybrid Design

1. **Spatial Feature Extractor** (CNN):
   - 2D convolutional blocks for spatial pattern extraction
   - Processes each time frame independently
   - Configurable depth and channels
   - Global average pooling for feature compression

2. **Temporal Model** (RNN):
   - Bidirectional GRU/LSTM for sequence modeling  
   - Captures temporal dependencies
   - Optional attention mechanism
   - Layer normalization and dropout

3. **Feature Fusion**:
   - Clinical feature processing network
   - Concatenation with temporal features
   - Fully connected fusion layers

4. **Multi-task Heads**:
   - **Risk Score Head**: 4 outputs with sigmoid activation
   - **Conclusion Head**: 1 output with BCEWithLogitsLoss

### Key Features

- **Modular Design**: Easy to modify individual components
- **Configurable Architecture**: All hyperparameters in config file
- **Multi-task Learning**: Shared representation learning
- **Advanced Loss Functions**: Focal loss, adaptive weighting

## Training

### Quick Start

1. **Prepare your data**:
   - Place MCG .txt files in a directory
   - Prepare Excel file with required sheets
   - Update paths in `configs/config.yaml`

2. **Start training**:
```bash
python train.py --config configs/config.yaml
```

3. **Monitor training**:
```bash
tensorboard --logdir runs/mcg_training_*/tensorboard
```

### Configuration

Edit `configs/config.yaml` to customize:

- **Data paths** and preprocessing options
- **Model architecture** parameters
- **Training hyperparameters** (learning rate, batch size, epochs)
- **Loss function** weights and types
- **Hardware settings** (GPU, workers)

### Training Features

- **TensorBoard Integration**: Real-time monitoring of metrics and losses
- **Early Stopping**: Prevents overfitting with configurable patience
- **Checkpointing**: Automatic saving of best models and regular checkpoints
- **Learning Rate Scheduling**: Cosine annealing, step decay, or plateau reduction
- **Gradient Clipping**: Stabilizes training of RNN components

### Output

Training creates a timestamped run directory containing:
- Model checkpoints
- TensorBoard logs  
- Training plots and visualizations
- Configuration backup
- Results summary

## Inference

### Batch Inference

Predict on multiple samples:
```bash
python inference.py \
    --checkpoint runs/mcg_training_*/checkpoints/best_model.pth \
    --data-path data/your_data.xlsx \
    --mcg-dir data/mcg_signals/ \
    --output predictions.csv
```

### Single Sample Inference

Predict on one sample:
```bash
python inference.py \
    --checkpoint runs/mcg_training_*/checkpoints/best_model.pth \
    --mcg-dir data/mcg_signals/ \
    --single-id "sample_001"
```

### Output Format

Predictions saved as CSV with columns:
- `mcg_id`: Sample identifier
- `LM_risk_score`, `LAD_risk_score`, `LCX_risk_score`, `RCA_risk_score`: Vessel risk scores (0-1)
- `conclusion_probability`: Conclusion probability (0-1)
- `conclusion_prediction`: Binary prediction (0/1)

## Evaluation

### Metrics

The model is evaluated using comprehensive metrics:

**Regression Metrics** (Risk Scores):
- Mean Squared Error (MSE)
- Mean Absolute Error (MAE)
- Root Mean Squared Error (RMSE)
- Per-vessel accuracy and AUC

**Classification Metrics** (Conclusion):
- Accuracy, Precision, Recall, F1-score
- Sensitivity and Specificity
- Area Under Curve (AUC)
- Confusion Matrix

### Visualization

Automatic generation of:
- Training and validation curves
- Confusion matrices
- ROC and Precision-Recall curves
- Prediction distribution plots
- Model performance comparisons

## Advanced Features

### Data Augmentation

Optional augmentation techniques:
- Gaussian noise injection
- Sinusoidal noise (20-50Hz) for MCG signals
- Mixup for classification tasks
- SMOTE for handling class imbalance

### Loss Functions

Multiple loss function options:
- **Standard Multi-task**: Weighted combination of regression and classification losses
- **Focal Loss**: Addresses class imbalance in conclusion prediction
- **Adaptive Weighting**: Learnable task weights using uncertainty
- **Combined Loss**: Includes contrastive learning for better representations

### Model Variants

Configurable model components:
- **Spatial Extractors**: Basic CNN, Enhanced CNN with residuals
- **Temporal Models**: GRU, LSTM, Transformer, with optional attention
- **Ensemble Models**: Multiple model combination

## Configuration Reference

### Key Configuration Sections

```yaml
data:
  xlsx_file: "path/to/data.xlsx"
  txt_data_dir: "path/to/mcg_signals"
  max_sequence_length: 2000
  preprocessing:
    normalization: "standard"  # standard, maxabs, minmax
    
model:
  architecture: "cnn_rnn"
  cnn:
    num_conv_blocks: 3
    base_channels: 32
  rnn:
    type: "GRU"  # GRU, LSTM
    hidden_size: 128
    bidirectional: true
    
training:
  batch_size: 32
  num_epochs: 100
  learning_rate: 0.001
  loss_weights:
    risk_score_weight: 1.0
    conclusion_weight: 1.0
```

## Troubleshooting

### Common Issues

1. **Out of Memory Errors**:
   - Reduce batch size in config
   - Decrease model size (fewer channels/layers)
   - Use gradient checkpointing

2. **Poor Convergence**:
   - Adjust learning rate
   - Check data preprocessing
   - Modify loss weights
   - Enable gradient clipping

3. **Data Loading Issues**:
   - Verify file paths in config
   - Check Excel sheet names
   - Ensure MCG files match IDs in Excel
   - Check MCG file format (tab-separated, 37 columns)

4. **Training Errors**:
   - **TypeError with early stopping**: Fixed - all numeric types properly cast
   - **Scaler not fitted**: Fixed - using sample-wise normalization
   - **Single label confusion matrix**: Fixed - proper label fallback handling
   - **Loss dropping to 0**: Fixed - preprocessing errors resolved

### Performance Optimization

- **Mixed Precision Training**: Enables faster training with minimal accuracy loss
- **Multiple Workers**: Increase `num_workers` for faster data loading
- **Batch Size Tuning**: Find optimal batch size for your GPU memory

## Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## Citation

If you use this code in your research, please cite:

```bibtex
@software{mcg_deep_learning,
  title={MCG Deep Learning Pipeline for Cardiovascular Risk Assessment},
  author={Your Name},
  year={2024},
  url={https://github.com/your-repo/mcg-deep-learning}
}
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

For questions or support, please contact [<EMAIL>]