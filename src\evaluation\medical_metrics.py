import torch
import numpy as np
import pandas as pd
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, 
    roc_auc_score, roc_curve, precision_recall_curve,
    confusion_matrix, classification_report,
    mean_squared_error, mean_absolute_error
)
from scipy import stats
from typing import Dict, List, Tuple, Any, Optional
import logging

logger = logging.getLogger(__name__)


class MedicalMetricsCalculator:
    """医学专用指标计算器"""
    
    def __init__(self):
        """初始化医学指标计算器"""
        self.vessel_names = ['LM', 'LAD', 'LCX', 'RCA']
        
    def calculate_binary_classification_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, 
                                              y_prob: np.ndarray) -> Dict[str, float]:
        """
        计算二分类的完整医学指标
        
        Args:
            y_true: 真实标签 (0/1)
            y_pred: 预测标签 (0/1)
            y_prob: 预测概率 (0-1)
            
        Returns:
            包含所有医学指标的字典
        """
        # 处理NaN值
        valid_mask = ~(np.isnan(y_true) | np.isnan(y_pred) | np.isnan(y_prob))
        if not valid_mask.any():
            return self._get_empty_binary_metrics()
        
        y_true_clean = y_true[valid_mask].astype(int)
        y_pred_clean = y_pred[valid_mask].astype(int)
        y_prob_clean = y_prob[valid_mask]
        
        metrics = {}
        
        try:
            # 基础分类指标
            metrics['accuracy'] = accuracy_score(y_true_clean, y_pred_clean)
            metrics['precision'] = precision_score(y_true_clean, y_pred_clean, zero_division=0)
            metrics['recall'] = recall_score(y_true_clean, y_pred_clean, zero_division=0)
            metrics['f1_score'] = f1_score(y_true_clean, y_pred_clean, zero_division=0)
            
            # 医学专用指标
            tn, fp, fn, tp = confusion_matrix(y_true_clean, y_pred_clean).ravel()
            
            metrics['sensitivity'] = tp / (tp + fn) if (tp + fn) > 0 else 0.0  # 敏感度/召回率
            metrics['specificity'] = tn / (tn + fp) if (tn + fp) > 0 else 0.0  # 特异度
            metrics['ppv'] = tp / (tp + fp) if (tp + fp) > 0 else 0.0  # 阳性预测值
            metrics['npv'] = tn / (tn + fn) if (tn + fn) > 0 else 0.0  # 阴性预测值
            
            # 似然比
            metrics['lr_positive'] = metrics['sensitivity'] / (1 - metrics['specificity']) if metrics['specificity'] < 1 else float('inf')
            metrics['lr_negative'] = (1 - metrics['sensitivity']) / metrics['specificity'] if metrics['specificity'] > 0 else float('inf')
            
            # AUC相关指标
            if len(np.unique(y_true_clean)) > 1:
                metrics['auc_roc'] = roc_auc_score(y_true_clean, y_prob_clean)
                
                # 计算95%置信区间 (DeLong方法简化版)
                auc_ci = self._calculate_auc_ci(y_true_clean, y_prob_clean)
                metrics['auc_ci_lower'] = auc_ci[0]
                metrics['auc_ci_upper'] = auc_ci[1]
                
                # Youden指数
                fpr, tpr, thresholds = roc_curve(y_true_clean, y_prob_clean)
                youden_index = tpr - fpr
                best_threshold_idx = np.argmax(youden_index)
                metrics['youden_index'] = youden_index[best_threshold_idx]
                metrics['optimal_threshold'] = thresholds[best_threshold_idx]
            else:
                metrics['auc_roc'] = 0.5
                metrics['auc_ci_lower'] = 0.5
                metrics['auc_ci_upper'] = 0.5
                metrics['youden_index'] = 0.0
                metrics['optimal_threshold'] = 0.5
            
            # 统计显著性
            metrics['sample_size'] = len(y_true_clean)
            metrics['positive_cases'] = int(np.sum(y_true_clean))
            metrics['negative_cases'] = int(len(y_true_clean) - np.sum(y_true_clean))
            
        except Exception as e:
            logger.warning(f"计算二分类指标时出错: {e}")
            return self._get_empty_binary_metrics()
        
        return metrics
    
    def calculate_regression_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, 
                                   vessel_name: str = '') -> Dict[str, float]:
        """
        计算回归任务的医学指标
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            vessel_name: 血管名称
            
        Returns:
            回归指标字典
        """
        # 处理NaN值
        valid_mask = ~(np.isnan(y_true) | np.isnan(y_pred))
        if not valid_mask.any():
            return self._get_empty_regression_metrics()
        
        y_true_clean = y_true[valid_mask]
        y_pred_clean = y_pred[valid_mask]
        
        metrics = {}
        
        try:
            # 基础回归指标
            metrics['mse'] = mean_squared_error(y_true_clean, y_pred_clean)
            metrics['rmse'] = np.sqrt(metrics['mse'])
            metrics['mae'] = mean_absolute_error(y_true_clean, y_pred_clean)
            
            # 相关性指标
            correlation, p_value = stats.pearsonr(y_true_clean, y_pred_clean)
            metrics['pearson_r'] = correlation
            metrics['pearson_p'] = p_value
            metrics['r_squared'] = correlation ** 2
            
            spearman_r, spearman_p = stats.spearmanr(y_true_clean, y_pred_clean)
            metrics['spearman_r'] = spearman_r
            metrics['spearman_p'] = spearman_p
            
            # 一致性指标
            metrics['mean_diff'] = np.mean(y_pred_clean - y_true_clean)  # 系统偏差
            metrics['std_diff'] = np.std(y_pred_clean - y_true_clean)    # 随机误差
            
            # 95%一致性界限 (Bland-Altman)
            mean_diff = metrics['mean_diff']
            std_diff = metrics['std_diff']
            metrics['loa_lower'] = mean_diff - 1.96 * std_diff
            metrics['loa_upper'] = mean_diff + 1.96 * std_diff
            
            # 平均绝对百分比误差
            with np.errstate(divide='ignore', invalid='ignore'):
                mape = np.mean(np.abs((y_true_clean - y_pred_clean) / y_true_clean)) * 100
                metrics['mape'] = mape if np.isfinite(mape) else 0.0
            
            # 作为分类任务的指标 (阈值0.5)
            y_true_binary = (y_true_clean >= 0.5).astype(int)
            y_pred_binary = (y_pred_clean >= 0.5).astype(int)
            
            if len(np.unique(y_true_binary)) > 1:
                metrics['binary_accuracy'] = accuracy_score(y_true_binary, y_pred_binary)
                metrics['binary_auc'] = roc_auc_score(y_true_binary, y_pred_clean)
            else:
                metrics['binary_accuracy'] = 0.0
                metrics['binary_auc'] = 0.5
            
            metrics['sample_size'] = len(y_true_clean)
            
        except Exception as e:
            logger.warning(f"计算回归指标时出错 ({vessel_name}): {e}")
            return self._get_empty_regression_metrics()
        
        return metrics
    
    def calculate_vessel_specific_metrics(self, predictions: Dict[str, torch.Tensor],
                                        targets: Dict[str, torch.Tensor]) -> Dict[str, Dict[str, float]]:
        """
        计算每个血管的特定指标
        
        Args:
            predictions: 预测结果字典
            targets: 真实标签字典
            
        Returns:
            每个血管的指标字典
        """
        vessel_metrics = {}
        
        # 获取数据
        risk_pred = predictions['risk_scores'].numpy()  # (N, 4)
        risk_target = targets['risk_scores'].numpy()   # (N, 4)
        
        for i, vessel in enumerate(self.vessel_names):
            vessel_pred = risk_pred[:, i]
            vessel_target = risk_target[:, i]
            
            # 计算该血管的回归指标
            regression_metrics = self.calculate_regression_metrics(
                vessel_target, vessel_pred, vessel_name=vessel
            )
            
            # 添加血管特定的分析
            vessel_specific = self._analyze_vessel_specific(vessel_target, vessel_pred, vessel)
            
            vessel_metrics[vessel] = {**regression_metrics, **vessel_specific}
        
        return vessel_metrics
    
    def calculate_conclusion_metrics(self, predictions: Dict[str, torch.Tensor],
                                   targets: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """
        计算造影结论的详细指标
        
        Args:
            predictions: 预测结果字典
            targets: 真实标签字典
            
        Returns:
            造影结论指标字典
        """
        conclusion_probs = predictions['conclusion_probs'].numpy().squeeze()
        conclusion_target = targets['conclusion'].numpy().squeeze()
        conclusion_pred = (conclusion_probs >= 0.5).astype(int)
        
        return self.calculate_binary_classification_metrics(
            conclusion_target, conclusion_pred, conclusion_probs
        )
    
    def generate_comprehensive_report(self, predictions: Dict[str, torch.Tensor],
                                    targets: Dict[str, torch.Tensor]) -> Dict[str, Any]:
        """
        生成综合医学评估报告
        
        Args:
            predictions: 预测结果字典
            targets: 真实标签字典
            
        Returns:
            综合评估报告
        """
        report = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'sample_size': len(predictions['risk_scores'])
        }
        
        # 造影结论指标
        report['conclusion_metrics'] = self.calculate_conclusion_metrics(predictions, targets)
        
        # 血管特异性指标
        report['vessel_metrics'] = self.calculate_vessel_specific_metrics(predictions, targets)
        
        # 整体风险评分指标
        risk_pred = predictions['risk_scores'].numpy()
        risk_target = targets['risk_scores'].numpy()
        
        # 所有血管的平均指标
        valid_mask = ~np.isnan(risk_target)
        if valid_mask.any():
            overall_regression = self.calculate_regression_metrics(
                risk_target[valid_mask], risk_pred[valid_mask], 'overall'
            )
            report['overall_risk_metrics'] = overall_regression
        
        # 临床相关分析
        report['clinical_analysis'] = self._perform_clinical_analysis(predictions, targets)
        
        # 性能总结
        report['performance_summary'] = self._create_performance_summary(report)
        
        return report
    
    def _calculate_auc_ci(self, y_true: np.ndarray, y_scores: np.ndarray, 
                         confidence_level: float = 0.95) -> Tuple[float, float]:
        """计算AUC的置信区间 (简化的bootstrap方法)"""
        n_bootstraps = 1000
        rng = np.random.RandomState(42)
        
        bootstrapped_scores = []
        
        for i in range(n_bootstraps):
            # Bootstrap采样
            indices = rng.randint(0, len(y_scores), len(y_scores))
            if len(np.unique(y_true[indices])) < 2:
                continue
                
            score = roc_auc_score(y_true[indices], y_scores[indices])
            bootstrapped_scores.append(score)
        
        if not bootstrapped_scores:
            return (0.5, 0.5)
        
        sorted_scores = np.array(bootstrapped_scores)
        alpha = 1.0 - confidence_level
        lower = np.percentile(sorted_scores, (alpha/2) * 100)
        upper = np.percentile(sorted_scores, (1 - alpha/2) * 100)
        
        return (lower, upper)
    
    def _analyze_vessel_specific(self, y_true: np.ndarray, y_pred: np.ndarray, 
                               vessel: str) -> Dict[str, float]:
        """血管特异性分析"""
        valid_mask = ~np.isnan(y_true)
        if not valid_mask.any():
            return {}
        
        y_true_clean = y_true[valid_mask]
        y_pred_clean = y_pred[valid_mask]
        
        analysis = {}
        
        # 不同狭窄程度的预测准确性
        severe_mask = y_true_clean >= 0.7  # 对应原始狭窄度70%以上
        moderate_mask = (y_true_clean >= 0.5) & (y_true_clean < 0.7)
        mild_mask = y_true_clean < 0.5
        
        for level, mask in [('severe', severe_mask), ('moderate', moderate_mask), ('mild', mild_mask)]:
            if mask.any():
                level_mae = mean_absolute_error(y_true_clean[mask], y_pred_clean[mask])
                analysis[f'{level}_stenosis_mae'] = level_mae
                analysis[f'{level}_stenosis_count'] = int(mask.sum())
        
        # 临床决策阈值分析
        thresholds = [0.5, 0.7]  # 对应50%和70%狭窄
        for threshold in thresholds:
            true_binary = (y_true_clean >= threshold).astype(int)
            pred_binary = (y_pred_clean >= threshold).astype(int)
            
            if len(np.unique(true_binary)) > 1:
                threshold_acc = accuracy_score(true_binary, pred_binary)
                analysis[f'threshold_{int(threshold*100)}_accuracy'] = threshold_acc
        
        return analysis
    
    def _perform_clinical_analysis(self, predictions: Dict[str, torch.Tensor],
                                 targets: Dict[str, torch.Tensor]) -> Dict[str, Any]:
        """执行临床相关分析"""
        analysis = {}
        
        # 多血管病变分析
        risk_target = targets['risk_scores'].numpy()
        risk_pred = predictions['risk_scores'].numpy()
        
        # 统计多血管病变情况 (阈值70%)
        severe_stenosis_target = (risk_target >= 0.7).astype(int)
        severe_stenosis_pred = (risk_pred >= 0.7).astype(int)
        
        # 多血管病变预测准确性
        multi_vessel_target = np.sum(severe_stenosis_target, axis=1) >= 2
        multi_vessel_pred = np.sum(severe_stenosis_pred, axis=1) >= 2
        
        if len(np.unique(multi_vessel_target)) > 1:
            analysis['multi_vessel_accuracy'] = accuracy_score(multi_vessel_target, multi_vessel_pred)
        
        # 左主干病变特殊分析 (LM是第0个)
        lm_target = risk_target[:, 0]
        lm_pred = risk_pred[:, 0]
        
        valid_lm = ~np.isnan(lm_target)
        if valid_lm.any():
            lm_severe = lm_target[valid_lm] >= 0.7
            if lm_severe.any():
                analysis['lm_severe_cases'] = int(lm_severe.sum())
                analysis['lm_severe_mae'] = mean_absolute_error(
                    lm_target[valid_lm][lm_severe], 
                    lm_pred[valid_lm][lm_severe]
                )
        
        return analysis
    
    def _create_performance_summary(self, report: Dict[str, Any]) -> Dict[str, str]:
        """创建性能总结"""
        summary = {}
        
        # 造影结论性能等级
        conclusion_auc = report.get('conclusion_metrics', {}).get('auc_roc', 0)
        if conclusion_auc >= 0.9:
            summary['conclusion_performance'] = 'Excellent'
        elif conclusion_auc >= 0.8:
            summary['conclusion_performance'] = 'Good'
        elif conclusion_auc >= 0.7:
            summary['conclusion_performance'] = 'Fair'
        else:
            summary['conclusion_performance'] = 'Poor'
        
        # 血管预测性能等级
        vessel_performances = []
        vessel_metrics = report.get('vessel_metrics', {})
        
        for vessel in self.vessel_names:
            if vessel in vessel_metrics:
                r_squared = vessel_metrics[vessel].get('r_squared', 0)
                if r_squared >= 0.8:
                    vessel_performances.append('Excellent')
                elif r_squared >= 0.6:
                    vessel_performances.append('Good')
                elif r_squared >= 0.4:
                    vessel_performances.append('Fair')
                else:
                    vessel_performances.append('Poor')
        
        if vessel_performances:
            from collections import Counter
            most_common = Counter(vessel_performances).most_common(1)[0][0]
            summary['vessel_performance'] = most_common
        
        # 整体评估
        conclusion_perf = summary.get('conclusion_performance', 'Poor')
        vessel_perf = summary.get('vessel_performance', 'Poor')
        
        perf_scores = {'Excellent': 4, 'Good': 3, 'Fair': 2, 'Poor': 1}
        avg_score = (perf_scores[conclusion_perf] + perf_scores[vessel_perf]) / 2
        
        if avg_score >= 3.5:
            summary['overall_performance'] = 'Excellent'
        elif avg_score >= 2.5:
            summary['overall_performance'] = 'Good'
        elif avg_score >= 1.5:
            summary['overall_performance'] = 'Fair'
        else:
            summary['overall_performance'] = 'Poor'
        
        return summary
    
    def _get_empty_binary_metrics(self) -> Dict[str, float]:
        """返回空的二分类指标"""
        return {
            'accuracy': 0.0, 'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0,
            'sensitivity': 0.0, 'specificity': 0.0, 'ppv': 0.0, 'npv': 0.0,
            'lr_positive': 0.0, 'lr_negative': 0.0, 'auc_roc': 0.5,
            'auc_ci_lower': 0.5, 'auc_ci_upper': 0.5, 'youden_index': 0.0,
            'optimal_threshold': 0.5, 'sample_size': 0, 'positive_cases': 0, 'negative_cases': 0
        }
    
    def _get_empty_regression_metrics(self) -> Dict[str, float]:
        """返回空的回归指标"""
        return {
            'mse': 0.0, 'rmse': 0.0, 'mae': 0.0, 'pearson_r': 0.0, 'pearson_p': 1.0,
            'r_squared': 0.0, 'spearman_r': 0.0, 'spearman_p': 1.0, 'mean_diff': 0.0,
            'std_diff': 0.0, 'loa_lower': 0.0, 'loa_upper': 0.0, 'mape': 0.0,
            'binary_accuracy': 0.0, 'binary_auc': 0.5, 'sample_size': 0
        }


def create_medical_evaluation_report(predictions: Dict[str, torch.Tensor],
                                   targets: Dict[str, torch.Tensor],
                                   save_path: Optional[str] = None) -> Dict[str, Any]:
    """
    创建医学评估报告的便捷函数
    
    Args:
        predictions: 模型预测结果
        targets: 真实标签
        save_path: 保存路径 (可选)
        
    Returns:
        医学评估报告
    """
    calculator = MedicalMetricsCalculator()
    report = calculator.generate_comprehensive_report(predictions, targets)
    
    if save_path:
        import json
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        logger.info(f"医学评估报告已保存到: {save_path}")
    
    return report