import os
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import logging
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns

logger = logging.getLogger(__name__)


class MCGDataValidator:
    """MCG数据验证和统计工具"""
    
    def __init__(self, xlsx_path: str, txt_data_dir: str):
        """
        初始化数据验证器
        
        Args:
            xlsx_path: Excel文件路径
            txt_data_dir: MCG txt文件目录路径
        """
        self.xlsx_path = xlsx_path
        self.txt_data_dir = txt_data_dir
        self.validation_results = {}
        
    def validate_excel_data(self) -> Dict[str, Any]:
        """验证Excel数据完整性和质量"""
        logger.info("开始验证Excel数据...")
        
        try:
            # 读取所有sheets
            train_df = pd.read_excel(self.xlsx_path, sheet_name="训练集")
            test_df = pd.read_excel(self.xlsx_path, sheet_name="内测集")
            vessel_df = pd.read_excel(self.xlsx_path, sheet_name="训测集三支信息")
            
            results = {
                'excel_readable': True,
                'sheets_found': ['训练集', '内测集', '训测集三支信息'],
                'train_samples': len(train_df),
                'test_samples': len(test_df),
                'vessel_info_samples': len(vessel_df),
                'train_columns': list(train_df.columns),
                'test_columns': list(test_df.columns),
                'vessel_columns': list(vessel_df.columns)
            }
            
            # 检查必要列
            required_vessel_cols = ['心磁号', 'LM', 'LAD', 'LCX', 'RCA']
            missing_vessel_cols = [col for col in required_vessel_cols if col not in vessel_df.columns]
            results['missing_vessel_columns'] = missing_vessel_cols
            
            # 检查心磁号列
            if '心磁号' in train_df.columns:
                results['train_mcg_ids'] = train_df['心磁号'].tolist()
                results['train_mcg_ids_count'] = len(results['train_mcg_ids'])
                results['train_unique_ids'] = train_df['心磁号'].nunique()
            
            if '心磁号' in test_df.columns:
                results['test_mcg_ids'] = test_df['心磁号'].tolist()
                results['test_mcg_ids_count'] = len(results['test_mcg_ids'])
                results['test_unique_ids'] = test_df['心磁号'].nunique()
            
            if '心磁号' in vessel_df.columns:
                results['vessel_mcg_ids'] = vessel_df['心磁号'].tolist()
                results['vessel_mcg_ids_count'] = len(results['vessel_mcg_ids'])
                results['vessel_unique_ids'] = vessel_df['心磁号'].nunique()
            
            # 检查临床特征列
            clinical_feature_cols = [col for col in train_df.columns if '临床特征' in col]
            results['clinical_feature_columns'] = clinical_feature_cols
            results['clinical_feature_count'] = len(clinical_feature_cols)
            
            # 检查造影结论列
            if '造影结论' in train_df.columns:
                conclusion_values = train_df['造影结论'].value_counts().to_dict()
                results['train_conclusion_distribution'] = conclusion_values
                results['train_conclusion_missing'] = train_df['造影结论'].isna().sum()
            
            if '造影结论' in test_df.columns:
                conclusion_values = test_df['造影结论'].value_counts().to_dict()
                results['test_conclusion_distribution'] = conclusion_values
                results['test_conclusion_missing'] = test_df['造影结论'].isna().sum()
            
            # 检查血管狭窄数据
            vessel_stats = {}
            for vessel in ['LM', 'LAD', 'LCX', 'RCA']:
                if vessel in vessel_df.columns:
                    vessel_data = vessel_df[vessel]
                    vessel_stats[vessel] = {
                        'count': vessel_data.count(),
                        'missing': vessel_data.isna().sum(),
                        'mean': float(vessel_data.mean()) if vessel_data.count() > 0 else None,
                        'std': float(vessel_data.std()) if vessel_data.count() > 0 else None,
                        'min': float(vessel_data.min()) if vessel_data.count() > 0 else None,
                        'max': float(vessel_data.max()) if vessel_data.count() > 0 else None,
                        'unique_values': int(vessel_data.nunique()) if vessel_data.count() > 0 else 0
                    }
            results['vessel_statistics'] = vessel_stats
            
            self.validation_results['excel_validation'] = results
            
            logger.info(f"Excel数据验证完成:")
            logger.info(f"  训练集样本数: {results.get('train_samples', 0)}")
            logger.info(f"  测试集样本数: {results.get('test_samples', 0)}")
            logger.info(f"  血管信息样本数: {results.get('vessel_info_samples', 0)}")
            logger.info(f"  临床特征数量: {results.get('clinical_feature_count', 0)}")
            
            return results
            
        except Exception as e:
            error_results = {
                'excel_readable': False,
                'error': str(e)
            }
            self.validation_results['excel_validation'] = error_results
            logger.error(f"Excel数据验证失败: {str(e)}")
            return error_results
    
    def validate_mcg_files(self) -> Dict[str, Any]:
        """验证MCG文件的可用性和数据质量"""
        logger.info("开始验证MCG文件...")
        
        # 获取所有txt文件
        txt_files = [f for f in os.listdir(self.txt_data_dir) if f.endswith('.txt')]
        txt_file_ids = [f.replace('.txt', '') for f in txt_files]
        
        results = {
            'txt_files_count': len(txt_files),
            'txt_file_ids': txt_file_ids[:100],  # 只保存前100个用于检查
            'txt_files_sample': txt_files[:10]  # 文件名示例
        }
        
        # 检查文件数据质量 (抽样检查)
        sample_files = txt_files[:min(50, len(txt_files))]  # 检查前50个文件
        file_stats = []
        
        for txt_file in sample_files:
            try:
                file_path = os.path.join(self.txt_data_dir, txt_file)
                
                # 读取文件
                data = np.loadtxt(file_path)
                
                if data.ndim == 2 and data.shape[0] > 0:
                    file_stat = {
                        'filename': txt_file,
                        'shape': data.shape,
                        'channels': data.shape[0] if data.shape[0] <= data.shape[1] else data.shape[1],
                        'timestamps': data.shape[1] if data.shape[0] <= data.shape[1] else data.shape[0],
                        'has_time_column': data[0, 0] == 0.0 and data[1, 0] == 1.0,  # 检查是否有时间列
                        'data_range': (float(data.min()), float(data.max())),
                        'has_nan': np.isnan(data).any(),
                        'file_size_kb': os.path.getsize(file_path) / 1024
                    }
                    file_stats.append(file_stat)
                    
            except Exception as e:
                logger.warning(f"无法读取文件 {txt_file}: {str(e)}")
                file_stats.append({
                    'filename': txt_file,
                    'error': str(e)
                })
        
        results['sample_file_statistics'] = file_stats
        
        # 统计数据质量
        valid_files = [stat for stat in file_stats if 'error' not in stat]
        if valid_files:
            shapes = [stat['shape'] for stat in valid_files]
            timestamps = [stat['timestamps'] for stat in valid_files]
            
            results['data_quality'] = {
                'valid_files_ratio': len(valid_files) / len(file_stats),
                'shape_consistency': len(set(str(shape[0]) for shape in shapes)) == 1,
                'common_shape': max(set(shapes), key=shapes.count) if shapes else None,
                'timestamp_stats': {
                    'min': min(timestamps) if timestamps else 0,
                    'max': max(timestamps) if timestamps else 0,
                    'mean': np.mean(timestamps) if timestamps else 0,
                    'std': np.std(timestamps) if timestamps else 0
                },
                'has_time_column_ratio': sum(stat.get('has_time_column', False) for stat in valid_files) / len(valid_files)
            }
        
        self.validation_results['mcg_validation'] = results
        
        logger.info(f"MCG文件验证完成:")
        logger.info(f"  文件总数: {results['txt_files_count']}")
        logger.info(f"  有效文件比例: {results.get('data_quality', {}).get('valid_files_ratio', 0):.2%}")
        
        return results
    
    def validate_data_consistency(self) -> Dict[str, Any]:
        """验证Excel数据与MCG文件的一致性"""
        logger.info("开始验证数据一致性...")
        
        excel_results = self.validation_results.get('excel_validation', {})
        mcg_results = self.validation_results.get('mcg_validation', {})
        
        if not excel_results.get('excel_readable') or not mcg_results.get('txt_files_count'):
            return {'error': '需要先完成Excel和MCG文件验证'}
        
        # 获取Excel中的心磁号
        train_ids = set(excel_results.get('train_mcg_ids', []))
        test_ids = set(excel_results.get('test_mcg_ids', []))
        vessel_ids = set(excel_results.get('vessel_mcg_ids', []))
        all_excel_ids = train_ids | test_ids | vessel_ids
        
        # 获取MCG文件ID
        mcg_file_ids = set(mcg_results.get('txt_file_ids', []))
        
        # 检查一致性
        matched_ids = all_excel_ids & mcg_file_ids
        excel_only_ids = all_excel_ids - mcg_file_ids
        mcg_only_ids = mcg_file_ids - all_excel_ids
        
        results = {
            'total_excel_ids': len(all_excel_ids),
            'total_mcg_file_ids': len(mcg_file_ids),
            'matched_ids_count': len(matched_ids),
            'excel_only_count': len(excel_only_ids),
            'mcg_only_count': len(mcg_only_ids),
            'match_ratio': len(matched_ids) / len(all_excel_ids) if all_excel_ids else 0,
            'excel_only_sample': list(excel_only_ids)[:20],  # 显示前20个
            'mcg_only_sample': list(mcg_only_ids)[:20]
        }
        
        # 各数据集的匹配情况
        results['train_match'] = {
            'total': len(train_ids),
            'matched': len(train_ids & mcg_file_ids),
            'missing': len(train_ids - mcg_file_ids),
            'ratio': len(train_ids & mcg_file_ids) / len(train_ids) if train_ids else 0
        }
        
        results['test_match'] = {
            'total': len(test_ids),
            'matched': len(test_ids & mcg_file_ids),
            'missing': len(test_ids - mcg_file_ids),
            'ratio': len(test_ids & mcg_file_ids) / len(test_ids) if test_ids else 0
        }
        
        results['vessel_match'] = {
            'total': len(vessel_ids),
            'matched': len(vessel_ids & mcg_file_ids),
            'missing': len(vessel_ids - mcg_file_ids),
            'ratio': len(vessel_ids & mcg_file_ids) / len(vessel_ids) if vessel_ids else 0
        }
        
        self.validation_results['consistency_validation'] = results
        
        logger.info(f"数据一致性验证完成:")
        logger.info(f"  Excel中总ID数: {results['total_excel_ids']}")
        logger.info(f"  MCG文件总数: {results['total_mcg_file_ids']}")
        logger.info(f"  匹配ID数: {results['matched_ids_count']}")
        logger.info(f"  匹配率: {results['match_ratio']:.2%}")
        
        return results
    
    def generate_data_summary(self) -> Dict[str, Any]:
        """生成数据概要报告"""
        summary = {
            'validation_timestamp': pd.Timestamp.now().isoformat(),
            'data_sources': {
                'excel_file': self.xlsx_path,
                'mcg_directory': self.txt_data_dir
            }
        }
        
        # 汇总统计
        excel_val = self.validation_results.get('excel_validation', {})
        mcg_val = self.validation_results.get('mcg_validation', {})
        consistency_val = self.validation_results.get('consistency_validation', {})
        
        summary['overall_statistics'] = {
            'excel_readable': excel_val.get('excel_readable', False),
            'mcg_files_available': mcg_val.get('txt_files_count', 0) > 0,
            'data_match_ratio': consistency_val.get('match_ratio', 0),
            'usable_train_samples': consistency_val.get('train_match', {}).get('matched', 0),
            'usable_test_samples': consistency_val.get('test_match', {}).get('matched', 0),
            'clinical_features_count': excel_val.get('clinical_feature_count', 0)
        }
        
        # 数据质量评估
        data_quality_score = 0
        max_score = 5
        
        if excel_val.get('excel_readable'):
            data_quality_score += 1
        if consistency_val.get('match_ratio', 0) > 0.8:
            data_quality_score += 1
        if mcg_val.get('data_quality', {}).get('valid_files_ratio', 0) > 0.9:
            data_quality_score += 1
        if excel_val.get('clinical_feature_count', 0) >= 10:
            data_quality_score += 1
        if all(consistency_val.get(f'{split}_match', {}).get('ratio', 0) > 0.7 
               for split in ['train', 'test', 'vessel']):
            data_quality_score += 1
        
        summary['data_quality_score'] = {
            'score': data_quality_score,
            'max_score': max_score,
            'rating': 'Excellent' if data_quality_score >= 4 else 
                     'Good' if data_quality_score >= 3 else 
                     'Fair' if data_quality_score >= 2 else 'Poor'
        }
        
        return summary
    
    def run_full_validation(self) -> Dict[str, Any]:
        """运行完整的数据验证流程"""
        logger.info("开始完整数据验证...")
        
        # 1. 验证Excel数据
        self.validate_excel_data()
        
        # 2. 验证MCG文件
        self.validate_mcg_files()
        
        # 3. 验证数据一致性
        self.validate_data_consistency()
        
        # 4. 生成总结报告
        summary = self.generate_data_summary()
        self.validation_results['summary'] = summary
        
        logger.info("数据验证完成!")
        logger.info(f"数据质量评分: {summary['data_quality_score']['score']}/{summary['data_quality_score']['max_score']} ({summary['data_quality_score']['rating']})")
        
        return self.validation_results
    
    def save_validation_report(self, output_path: str):
        """保存验证报告到文件"""
        import json
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.validation_results, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"验证报告已保存到: {output_path}")
    
    def create_validation_plots(self, save_dir: str):
        """创建验证相关的可视化图表"""
        os.makedirs(save_dir, exist_ok=True)
        
        # 1. 数据匹配情况图
        consistency_val = self.validation_results.get('consistency_validation', {})
        if consistency_val:
            fig, axes = plt.subplots(1, 3, figsize=(15, 5))
            
            splits = ['train', 'test', 'vessel']
            colors = ['skyblue', 'lightcoral', 'lightgreen']
            
            for i, split in enumerate(splits):
                split_data = consistency_val.get(f'{split}_match', {})
                matched = split_data.get('matched', 0)
                missing = split_data.get('missing', 0)
                
                axes[i].pie([matched, missing], labels=['Matched', 'Missing'], 
                          colors=[colors[i], 'lightgray'], autopct='%1.1f%%')
                axes[i].set_title(f'{split.title()} Set Data Matching')
            
            plt.tight_layout()
            plt.savefig(os.path.join(save_dir, 'data_matching.png'), dpi=300, bbox_inches='tight')
            plt.close()
        
        # 2. 血管狭窄数据分布图
        excel_val = self.validation_results.get('excel_validation', {})
        vessel_stats = excel_val.get('vessel_statistics', {})
        
        if vessel_stats:
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            vessels = ['LM', 'LAD', 'LCX', 'RCA']
            
            for i, vessel in enumerate(vessels):
                row, col = i // 2, i % 2
                if vessel in vessel_stats:
                    stats = vessel_stats[vessel]
                    
                    # 显示基本统计信息
                    info_text = f"Count: {stats.get('count', 0)}\n"
                    info_text += f"Missing: {stats.get('missing', 0)}\n"
                    info_text += f"Mean: {stats.get('mean', 0):.2f}\n"
                    info_text += f"Std: {stats.get('std', 0):.2f}"
                    
                    axes[row, col].text(0.1, 0.5, info_text, transform=axes[row, col].transAxes,
                                      fontsize=12, verticalalignment='center',
                                      bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
                    axes[row, col].set_title(f'{vessel} Vessel Statistics')
                    axes[row, col].set_xlim(0, 1)
                    axes[row, col].set_ylim(0, 1)
                    axes[row, col].axis('off')
            
            plt.tight_layout()
            plt.savefig(os.path.join(save_dir, 'vessel_statistics.png'), dpi=300, bbox_inches='tight')
            plt.close()
        
        logger.info(f"验证图表已保存到: {save_dir}")


def quick_data_check(xlsx_path: str, txt_data_dir: str) -> Dict[str, Any]:
    """快速数据检查函数"""
    validator = MCGDataValidator(xlsx_path, txt_data_dir)
    results = validator.run_full_validation()
    
    print("\n=== MCG数据验证报告 ===")
    summary = results.get('summary', {})
    overall = summary.get('overall_statistics', {})
    quality = summary.get('data_quality_score', {})
    
    print(f"Excel文件可读: {overall.get('excel_readable', False)}")
    print(f"MCG文件可用: {overall.get('mcg_files_available', False)}")
    print(f"数据匹配率: {overall.get('data_match_ratio', 0):.2%}")
    print(f"可用训练样本: {overall.get('usable_train_samples', 0)}")
    print(f"可用测试样本: {overall.get('usable_test_samples', 0)}")
    print(f"临床特征数量: {overall.get('clinical_features_count', 0)}")
    print(f"数据质量评分: {quality.get('score', 0)}/{quality.get('max_score', 5)} ({quality.get('rating', 'Unknown')})")
    
    return results