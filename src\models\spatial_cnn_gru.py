"""
空间感知的CNN+GRU模型 - 支持6x6和36x36空间数据
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, Tuple
import logging

logger = logging.getLogger(__name__)


class Spatial2DCNNProcessor(nn.Module):
    """2D CNN处理器 - 处理空间MCG数据"""
    
    def __init__(self, spatial_size: int = 6, base_channels: int = 64, 
                 num_blocks: int = 4, dropout: float = 0.2):
        """
        初始化2D CNN处理器
        
        Args:
            spatial_size: 空间尺寸 (6 或 36)
            base_channels: 基础通道数
            num_blocks: CNN块数量
            dropout: Dropout率
        """
        super().__init__()
        
        self.spatial_size = spatial_size
        self.input_channels = 1  # 单通道输入
        
        # 构建2D CNN块
        self.blocks = nn.ModuleList()
        in_ch = self.input_channels
        
        for i in range(num_blocks):
            out_ch = base_channels * (2 ** i)
            
            # 2D卷积块
            block = nn.Sequential(
                # 第一个2D卷积
                nn.Conv2d(in_ch, out_ch, kernel_size=3, padding=1, bias=False),
                nn.BatchNorm2d(out_ch),
                nn.GELU(),
                nn.Dropout2d(dropout),
                
                # 第二个2D卷积
                nn.Conv2d(out_ch, out_ch, kernel_size=3, padding=1, bias=False),
                nn.BatchNorm2d(out_ch),
                nn.GELU(),
                nn.Dropout2d(dropout),
                
                # 池化 - 对小尺寸数据更谨慎
                nn.MaxPool2d(2) if i < num_blocks - 2 and spatial_size >= 12 else (
                    nn.AdaptiveAvgPool2d((1, 1)) if i == num_blocks - 1 else nn.Identity()
                )
            )
            
            self.blocks.append(block)
            in_ch = out_ch
        
        self.output_dim = base_channels * (2 ** (num_blocks - 1))
        
        logger.info(f"Spatial2DCNNProcessor: {spatial_size}x{spatial_size} → {self.output_dim}D, blocks={num_blocks}")
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: (batch_size, seq_len, 1, H, W) 空间数据
            
        Returns:
            (batch_size, seq_len, output_dim) 特征
        """
        batch_size, seq_len = x.shape[:2]
        
        # 重整形状以进行2D卷积: (batch_size * seq_len, 1, H, W)
        x = x.view(-1, *x.shape[2:])
        
        # 通过2D CNN块
        for block in self.blocks:
            x = block(x)
        
        # 展平空间维度: (batch_size * seq_len, output_dim)
        x = x.view(x.size(0), -1)
        
        # 恢复时序维度: (batch_size, seq_len, output_dim)
        x = x.view(batch_size, seq_len, self.output_dim)
        
        return x


class Timepoint1DCNNProcessor(nn.Module):
    """1D CNN处理器 - 处理时刻点数据"""
    
    def __init__(self, input_channels: int = 6, hidden_dim: int = 128, 
                 num_layers: int = 3, dropout: float = 0.2):
        """
        初始化1D CNN处理器
        
        Args:
            input_channels: 输入通道数 (6个时刻点)
            hidden_dim: 隐藏层维度
            num_layers: 层数
            dropout: Dropout率
        """
        super().__init__()
        
        layers = []
        in_ch = input_channels
        
        for i in range(num_layers):
            layers.extend([
                nn.Conv1d(in_ch, hidden_dim, kernel_size=7, padding=3, bias=False),
                nn.BatchNorm1d(hidden_dim),
                nn.GELU(),
                nn.Dropout1d(dropout)
            ])
            in_ch = hidden_dim
        
        layers.append(nn.AdaptiveAvgPool1d(1))
        self.processor = nn.Sequential(*layers)
        self.output_dim = hidden_dim
        
        logger.info(f"Timepoint1DCNNProcessor: {input_channels} → {hidden_dim}D, layers={num_layers}")
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: (batch_size, seq_len, 6) 时刻点数据
            
        Returns:
            (batch_size, hidden_dim) 特征
        """
        # 转置用于1D卷积: (batch_size, 6, seq_len)
        x = x.transpose(1, 2)
        
        # 1D CNN处理
        x = self.processor(x)  # (batch_size, hidden_dim, 1)
        
        return x.squeeze(-1)  # (batch_size, hidden_dim)


class SpatialTemporalGRU(nn.Module):
    """空间时序GRU编码器"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 512, 
                 num_layers: int = 2, dropout: float = 0.3):
        """
        初始化GRU编码器
        
        Args:
            input_dim: 输入维度
            hidden_dim: 隐藏层维度
            num_layers: GRU层数
            dropout: Dropout率
        """
        super().__init__()
        
        self.gru = nn.GRU(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0.0,
            batch_first=True,
            bidirectional=True
        )
        
        self.layer_norm = nn.LayerNorm(hidden_dim * 2)
        self.dropout = nn.Dropout(dropout)
        self.output_dim = hidden_dim * 2
        
        logger.info(f"SpatialTemporalGRU: {input_dim} → {self.output_dim}D, layers={num_layers}")
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: (batch_size, seq_len, input_dim)
            
        Returns:
            (batch_size, output_dim) 序列特征
        """
        # GRU处理
        gru_out, _ = self.gru(x)  # (batch_size, seq_len, hidden_dim * 2)
        
        # 取最后时刻的输出
        last_output = gru_out[:, -1, :]  # (batch_size, hidden_dim * 2)
        
        # 层归一化和dropout
        output = self.layer_norm(last_output)
        output = self.dropout(output)
        
        return output


class SpatialAwareMCGModel(nn.Module):
    """空间感知的MCG模型"""
    
    def __init__(self, config: Dict[str, Any], clinical_feature_dim: int = 0):
        """
        初始化空间感知MCG模型
        
        Args:
            config: 模型配置
            clinical_feature_dim: 临床特征维度
        """
        super().__init__()
        
        self.clinical_feature_dim = clinical_feature_dim
        
        # 解析配置
        spatial_config = config.get('spatial', {})
        timepoint_config = config.get('timepoint', {})
        gru_config = config.get('gru', {})
        fusion_config = config.get('fusion', {})
        clinical_config = config.get('clinical', {})
        output_config = config.get('output', {})
        
        spatial_size = spatial_config.get('spatial_size', 6)
        
        # 空间2D CNN处理器
        self.spatial_processor = Spatial2DCNNProcessor(
            spatial_size=spatial_size,
            base_channels=spatial_config.get('base_channels', 64),
            num_blocks=spatial_config.get('num_blocks', 4),
            dropout=spatial_config.get('dropout', 0.2)
        )
        
        # 时刻点1D CNN处理器
        self.timepoint_processor = Timepoint1DCNNProcessor(
            input_channels=6,
            hidden_dim=timepoint_config.get('hidden_dim', 128),
            num_layers=timepoint_config.get('num_layers', 3),
            dropout=timepoint_config.get('dropout', 0.2)
        )
        
        # 空间时序GRU编码器
        self.gru_encoder = SpatialTemporalGRU(
            input_dim=self.spatial_processor.output_dim,
            hidden_dim=gru_config.get('hidden_dim', 256),
            num_layers=gru_config.get('num_layers', 2),
            dropout=gru_config.get('dropout', 0.3)
        )
        
        # 特征融合维度
        fused_dim = self.gru_encoder.output_dim + self.timepoint_processor.output_dim
        
        # 临床特征处理器
        if self.clinical_feature_dim > 0:
            self.clinical_processor = nn.Sequential(
                nn.Linear(clinical_feature_dim, clinical_config.get('hidden_dim', 64)),
                nn.ReLU(),
                nn.Dropout(clinical_config.get('dropout', 0.3)),
                nn.Linear(clinical_config.get('hidden_dim', 64), clinical_config.get('hidden_dim', 64))
            )
            fused_dim += clinical_config.get('hidden_dim', 64)
        
        # 最终融合层
        self.final_fusion = nn.Sequential(
            nn.Linear(fused_dim, fusion_config.get('hidden_dim', 512)),
            nn.ReLU(),
            nn.Dropout(fusion_config.get('dropout', 0.3)),
            nn.Linear(fusion_config.get('hidden_dim', 512), fusion_config.get('hidden_dim', 512))
        )
        
        # 输出头
        final_dim = fusion_config.get('hidden_dim', 512)
        
        # 风险分数头 (4个血管)
        self.risk_head = nn.Sequential(
            nn.Linear(final_dim, output_config.get('risk_head_hidden', 256)),
            nn.ReLU(),
            nn.Dropout(output_config.get('dropout', 0.4)),
            nn.Linear(output_config.get('risk_head_hidden', 256), 4),
            nn.Sigmoid()
        )
        
        # 结论头 (二分类)
        self.conclusion_head = nn.Sequential(
            nn.Linear(final_dim, output_config.get('conclusion_head_hidden', 128)),
            nn.ReLU(),
            nn.Dropout(output_config.get('dropout', 0.4)),
            nn.Linear(output_config.get('conclusion_head_hidden', 128), 1)
        )
        
        # 计算总参数
        total_params = sum(p.numel() for p in self.parameters())
        logger.info(f"SpatialAwareMCGModel initialized: {total_params:,} parameters")
        logger.info(f"Spatial size: {spatial_size}x{spatial_size}")
        logger.info(f"Clinical features: {clinical_feature_dim}")
    
    def forward(self, spatial_data: torch.Tensor, timepoint_data: torch.Tensor, 
                clinical_features: torch.Tensor = None) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            spatial_data: (batch_size, seq_len, 1, H, W) 空间数据
            timepoint_data: (batch_size, seq_len, 6) 时刻点数据
            clinical_features: (batch_size, clinical_dim) 临床特征
            
        Returns:
            预测结果字典
        """
        # 处理空间数据
        spatial_features = self.spatial_processor(spatial_data)  # (batch_size, seq_len, spatial_dim)
        
        # 处理时刻点数据
        timepoint_features = self.timepoint_processor(timepoint_data)  # (batch_size, timepoint_dim)
        
        # GRU编码空间时序特征
        spatial_temporal_features = self.gru_encoder(spatial_features)  # (batch_size, gru_dim)
        
        # 融合空间时序特征和时刻点特征
        combined_features = torch.cat([spatial_temporal_features, timepoint_features], dim=1)
        
        # 融合临床特征
        if self.clinical_feature_dim > 0 and clinical_features is not None and clinical_features.numel() > 0:
            clinical_processed = self.clinical_processor(clinical_features)
            combined_features = torch.cat([combined_features, clinical_processed], dim=1)
        
        # 最终融合
        final_features = self.final_fusion(combined_features)
        
        # 多任务预测
        risk_scores = self.risk_head(final_features)
        conclusion_logits = self.conclusion_head(final_features)
        
        return {
            'risk_scores': risk_scores,
            'conclusion_logits': conclusion_logits,
            'fused_features': final_features,
            'spatial_features': spatial_temporal_features,
            'timepoint_features': timepoint_features
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        return {
            'architecture': 'SpatialAwareMCGModel',
            'total_parameters': total_params,
            'clinical_feature_dim': self.clinical_feature_dim,
            'spatial_processor_dim': self.spatial_processor.output_dim,
            'timepoint_processor_dim': self.timepoint_processor.output_dim,
            'gru_encoder_dim': self.gru_encoder.output_dim,
            'optimization_features': [
                'spatial_2d_cnn',
                'timepoint_1d_cnn', 
                'bidirectional_gru',
                'multi_scale_fusion',
                'clinical_integration'
            ]
        }


def create_spatial_aware_model(config: Dict[str, Any], clinical_feature_dim: int = 0) -> SpatialAwareMCGModel:
    """创建空间感知模型"""
    return SpatialAwareMCGModel(config, clinical_feature_dim)


def test_spatial_model():
    """测试空间感知模型"""
    print("🧪 测试空间感知模型")
    
    # 模型配置
    config = {
        'spatial': {'spatial_size': 6, 'base_channels': 32, 'num_blocks': 3},
        'timepoint': {'hidden_dim': 64, 'num_layers': 2},
        'gru': {'hidden_dim': 128, 'num_layers': 2},
        'fusion': {'hidden_dim': 256},
        'clinical': {'hidden_dim': 32},
        'output': {'risk_head_hidden': 128, 'conclusion_head_hidden': 64}
    }
    
    # 创建模型
    model = create_spatial_aware_model(config, clinical_feature_dim=12)
    model.eval()
    
    # 创建测试数据
    batch_size = 4
    seq_len = 1200
    
    # 6x6空间数据
    spatial_data = torch.randn(batch_size, seq_len, 1, 6, 6)
    timepoint_data = torch.randn(batch_size, seq_len, 6)
    clinical_features = torch.randn(batch_size, 12)
    
    print(f"输入数据:")
    print(f"  空间数据: {spatial_data.shape}")
    print(f"  时刻点数据: {timepoint_data.shape}")
    print(f"  临床特征: {clinical_features.shape}")
    
    # 前向传播
    with torch.no_grad():
        output = model(spatial_data, timepoint_data, clinical_features)
    
    print(f"\\n输出:")
    print(f"  风险分数: {output['risk_scores'].shape}")
    print(f"  结论预测: {output['conclusion_logits'].shape}")
    
    print(f"\\n模型信息:")
    info = model.get_model_info()
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    return True


if __name__ == "__main__":
    test_spatial_model()