import torch
import numpy as np
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, 
    roc_auc_score, mean_squared_error, mean_absolute_error,
    confusion_matrix, roc_curve, precision_recall_curve
)
from sklearn.preprocessing import label_binarize
from typing import Dict, List, Tuple, Any, Optional
import logging

logger = logging.getLogger(__name__)


class MetricsCalculator:
    """Calculate various evaluation metrics for MCG model."""
    
    def __init__(self, metric_names: List[str]):
        """
        Initialize metrics calculator.
        
        Args:
            metric_names: List of metric names to calculate
        """
        self.metric_names = metric_names
        self.supported_metrics = {
            'accuracy', 'precision', 'recall', 'f1', 'auc', 'sensitivity', 
            'specificity', 'mse', 'mae', 'rmse'
        }
        
        # Validate metric names
        unsupported = set(metric_names) - self.supported_metrics
        if unsupported:
            logger.warning(f"Unsupported metrics will be ignored: {unsupported}")
        
        self.metric_names = [m for m in metric_names if m in self.supported_metrics]
        logger.info(f"MetricsCalculator initialized with metrics: {self.metric_names}")
    
    def calculate_metrics(self, predictions: Dict[str, torch.Tensor], 
                         targets: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """
        Calculate all specified metrics.
        
        Args:
            predictions: Dictionary containing model predictions
                - 'risk_scores': (N, 4) predicted risk scores
                - 'conclusion_probs': (N, 1) predicted conclusion probabilities
            targets: Dictionary containing ground truth labels
                - 'risk_scores': (N, 4) target risk scores
                - 'conclusion': (N, 1) target conclusion labels
                
        Returns:
            Dictionary of metric values
        """
        metrics = {}
        
        # Convert to numpy for sklearn compatibility
        risk_pred = predictions['risk_scores'].numpy()
        risk_target = targets['risk_scores'].numpy()
        conclusion_probs = predictions['conclusion_probs'].numpy().squeeze()
        conclusion_target = targets['conclusion'].numpy().squeeze()
        
        # Binary predictions for conclusion (threshold = 0.5)
        conclusion_pred = (conclusion_probs >= 0.5).astype(int)
        
        # Calculate regression metrics for risk scores
        self._calculate_regression_metrics(metrics, risk_pred, risk_target)
        
        # Calculate classification metrics for conclusion
        self._calculate_classification_metrics(
            metrics, conclusion_pred, conclusion_target, conclusion_probs
        )
        
        # Calculate per-vessel metrics
        self._calculate_per_vessel_metrics(metrics, risk_pred, risk_target)
        
        return metrics
    
    def _calculate_regression_metrics(self, metrics: Dict[str, float], 
                                    predictions: np.ndarray, targets: np.ndarray):
        """Calculate regression metrics for risk scores."""
        # Handle NaN values
        valid_mask = ~np.isnan(targets)
        if not valid_mask.any():
            logger.warning("No valid risk score targets found")
            return
        
        valid_pred = predictions[valid_mask]
        valid_target = targets[valid_mask]
        
        if 'mse' in self.metric_names:
            metrics['risk_mse'] = mean_squared_error(valid_target, valid_pred)
        
        if 'mae' in self.metric_names:
            metrics['risk_mae'] = mean_absolute_error(valid_target, valid_pred)
        
        if 'rmse' in self.metric_names:
            metrics['risk_rmse'] = np.sqrt(mean_squared_error(valid_target, valid_pred))
    
    def _calculate_classification_metrics(self, metrics: Dict[str, float],
                                        predictions: np.ndarray, targets: np.ndarray,
                                        probabilities: np.ndarray):
        """Calculate classification metrics for conclusion."""
        # Handle NaN values
        valid_mask = ~np.isnan(targets)
        if not valid_mask.any():
            logger.warning("No valid conclusion targets found")
            return
        
        valid_pred = predictions[valid_mask]
        valid_target = targets[valid_mask].astype(int)
        valid_probs = probabilities[valid_mask]
        
        # Basic classification metrics
        if 'accuracy' in self.metric_names:
            metrics['conclusion_accuracy'] = accuracy_score(valid_target, valid_pred)
        
        if 'precision' in self.metric_names:
            metrics['conclusion_precision'] = precision_score(
                valid_target, valid_pred, average='binary', zero_division=0
            )
        
        if 'recall' in self.metric_names:
            metrics['conclusion_recall'] = recall_score(
                valid_target, valid_pred, average='binary', zero_division=0
            )
        
        if 'f1' in self.metric_names:
            metrics['conclusion_f1'] = f1_score(
                valid_target, valid_pred, average='binary', zero_division=0
            )
        
        # Sensitivity and Specificity
        if 'sensitivity' in self.metric_names or 'specificity' in self.metric_names:
            tn, fp, fn, tp = confusion_matrix(valid_target, valid_pred).ravel()
            
            if 'sensitivity' in self.metric_names:
                sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
                metrics['conclusion_sensitivity'] = sensitivity
            
            if 'specificity' in self.metric_names:
                specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
                metrics['conclusion_specificity'] = specificity
        
        # AUC
        if 'auc' in self.metric_names and len(np.unique(valid_target)) > 1:
            try:
                metrics['conclusion_auc'] = roc_auc_score(valid_target, valid_probs)
            except ValueError as e:
                logger.warning(f"Could not calculate AUC: {e}")
                metrics['conclusion_auc'] = 0.0
    
    def _calculate_per_vessel_metrics(self, metrics: Dict[str, float],
                                    predictions: np.ndarray, targets: np.ndarray):
        """Calculate per-vessel metrics for risk scores."""
        vessel_names = ['LM', 'LAD', 'LCX', 'RCA']
        
        for i, vessel in enumerate(vessel_names):
            # Handle NaN values for this vessel
            valid_mask = ~np.isnan(targets[:, i])
            if not valid_mask.any():
                continue
            
            vessel_pred = predictions[valid_mask, i]
            vessel_target = targets[valid_mask, i]
            
            if 'mse' in self.metric_names:
                metrics[f'{vessel}_mse'] = mean_squared_error(vessel_target, vessel_pred)
            
            if 'mae' in self.metric_names:
                metrics[f'{vessel}_mae'] = mean_absolute_error(vessel_target, vessel_pred)
            
            # Convert to binary classification for additional metrics
            vessel_pred_binary = (vessel_pred >= 0.5).astype(int)
            vessel_target_binary = (vessel_target >= 0.5).astype(int)
            
            if len(np.unique(vessel_target_binary)) > 1:
                if 'accuracy' in self.metric_names:
                    metrics[f'{vessel}_accuracy'] = accuracy_score(
                        vessel_target_binary, vessel_pred_binary
                    )
                
                if 'auc' in self.metric_names:
                    try:
                        metrics[f'{vessel}_auc'] = roc_auc_score(
                            vessel_target_binary, vessel_pred
                        )
                    except ValueError:
                        metrics[f'{vessel}_auc'] = 0.0
    
    def calculate_detailed_metrics(self, predictions: Dict[str, torch.Tensor],
                                 targets: Dict[str, torch.Tensor]) -> Dict[str, Any]:
        """
        Calculate detailed metrics including curves and confusion matrices.
        
        Args:
            predictions: Model predictions
            targets: Ground truth targets
            
        Returns:
            Dictionary with detailed metrics and curves
        """
        detailed_metrics = {}
        
        # Convert to numpy
        conclusion_probs = predictions['conclusion_probs'].numpy().squeeze()
        conclusion_target = targets['conclusion'].numpy().squeeze()
        
        # Handle NaN values
        valid_mask = ~np.isnan(conclusion_target)
        if not valid_mask.any():
            return detailed_metrics
        
        valid_probs = conclusion_probs[valid_mask]
        valid_target = conclusion_target[valid_mask].astype(int)
        valid_pred = (valid_probs >= 0.5).astype(int)
        
        # Confusion matrix
        if len(np.unique(valid_target)) > 1:
            cm = confusion_matrix(valid_target, valid_pred)
            detailed_metrics['confusion_matrix'] = cm
            
            # ROC curve
            try:
                fpr, tpr, roc_thresholds = roc_curve(valid_target, valid_probs)
                detailed_metrics['roc_curve'] = {
                    'fpr': fpr,
                    'tpr': tpr,
                    'thresholds': roc_thresholds
                }
            except ValueError:
                logger.warning("Could not calculate ROC curve")
            
            # Precision-Recall curve
            try:
                precision, recall, pr_thresholds = precision_recall_curve(valid_target, valid_probs)
                detailed_metrics['pr_curve'] = {
                    'precision': precision,
                    'recall': recall,
                    'thresholds': pr_thresholds
                }
            except ValueError:
                logger.warning("Could not calculate PR curve")
        
        return detailed_metrics


class ModelEvaluator:
    """Comprehensive model evaluator."""
    
    def __init__(self, model: torch.nn.Module, device: torch.device, 
                 metric_names: List[str]):
        """
        Initialize model evaluator.
        
        Args:
            model: Trained model
            device: Evaluation device
            metric_names: List of metrics to calculate
        """
        self.model = model
        self.device = device
        self.metrics_calculator = MetricsCalculator(metric_names)
        
    def evaluate(self, data_loader: torch.utils.data.DataLoader, 
                return_predictions: bool = False) -> Dict[str, Any]:
        """
        Evaluate model on data loader.
        
        Args:
            data_loader: Data loader for evaluation
            return_predictions: Whether to return predictions
            
        Returns:
            Dictionary containing metrics and optionally predictions
        """
        self.model.eval()
        
        all_predictions = {'risk_scores': [], 'conclusion_probs': []}
        all_targets = {'risk_scores': [], 'conclusion': []}
        all_mcg_ids = []
        
        with torch.no_grad():
            for batch in data_loader:
                # Move batch to device
                mcg_data = batch['mcg_data'].to(self.device)
                clinical_features = batch['clinical_features'].to(self.device)
                risk_scores = batch['risk_scores']
                conclusions = batch['conclusion']
                mcg_ids = batch['mcg_ids']
                
                # Forward pass
                predictions = self.model(mcg_data, clinical_features)
                
                # Collect predictions and targets
                all_predictions['risk_scores'].append(predictions['risk_scores'].cpu())
                all_predictions['conclusion_probs'].append(
                    torch.sigmoid(predictions['conclusion_logits']).cpu()
                )
                all_targets['risk_scores'].append(risk_scores)
                all_targets['conclusion'].append(conclusions)
                all_mcg_ids.extend(mcg_ids)
        
        # Concatenate all predictions and targets
        all_predictions['risk_scores'] = torch.cat(all_predictions['risk_scores'], dim=0)
        all_predictions['conclusion_probs'] = torch.cat(all_predictions['conclusion_probs'], dim=0)
        all_targets['risk_scores'] = torch.cat(all_targets['risk_scores'], dim=0)
        all_targets['conclusion'] = torch.cat(all_targets['conclusion'], dim=0)
        
        # Calculate metrics
        metrics = self.metrics_calculator.calculate_metrics(all_predictions, all_targets)
        detailed_metrics = self.metrics_calculator.calculate_detailed_metrics(
            all_predictions, all_targets
        )
        
        results = {
            'metrics': metrics,
            'detailed_metrics': detailed_metrics,
            'num_samples': len(all_mcg_ids)
        }
        
        if return_predictions:
            results['predictions'] = all_predictions
            results['targets'] = all_targets
            results['mcg_ids'] = all_mcg_ids
        
        return results
    
    def evaluate_single_sample(self, mcg_data: torch.Tensor, 
                              clinical_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Evaluate model on a single sample.
        
        Args:
            mcg_data: MCG data tensor
            clinical_features: Clinical features tensor
            
        Returns:
            Model predictions
        """
        self.model.eval()
        
        with torch.no_grad():
            # Add batch dimension
            mcg_data = mcg_data.unsqueeze(0).to(self.device)
            clinical_features = clinical_features.unsqueeze(0).to(self.device)
            
            # Forward pass
            predictions = self.model(mcg_data, clinical_features)
            
            # Remove batch dimension and convert to probabilities
            results = {
                'risk_scores': predictions['risk_scores'].squeeze(0).cpu(),
                'conclusion_prob': torch.sigmoid(predictions['conclusion_logits']).squeeze(0).cpu(),
                'conclusion_pred': (torch.sigmoid(predictions['conclusion_logits']) >= 0.5).squeeze(0).cpu()
            }
        
        return results


def calculate_bootstrap_metrics(predictions: Dict[str, torch.Tensor],
                              targets: Dict[str, torch.Tensor],
                              metric_names: List[str],
                              n_bootstrap: int = 1000,
                              confidence_level: float = 0.95) -> Dict[str, Dict[str, float]]:
    """
    Calculate bootstrap confidence intervals for metrics.
    
    Args:
        predictions: Model predictions
        targets: Ground truth targets
        metric_names: List of metrics to calculate
        n_bootstrap: Number of bootstrap samples
        confidence_level: Confidence level for intervals
        
    Returns:
        Dictionary with metric statistics
    """
    metrics_calculator = MetricsCalculator(metric_names)
    
    # Convert to numpy
    n_samples = len(predictions['conclusion_probs'])
    bootstrap_metrics = {metric: [] for metric in metric_names}
    
    for _ in range(n_bootstrap):
        # Bootstrap sampling
        indices = np.random.choice(n_samples, size=n_samples, replace=True)
        
        bootstrap_pred = {
            'risk_scores': predictions['risk_scores'][indices],
            'conclusion_probs': predictions['conclusion_probs'][indices]
        }
        bootstrap_target = {
            'risk_scores': targets['risk_scores'][indices],
            'conclusion': targets['conclusion'][indices]
        }
        
        # Calculate metrics for bootstrap sample
        sample_metrics = metrics_calculator.calculate_metrics(bootstrap_pred, bootstrap_target)
        
        for metric in metric_names:
            if f'conclusion_{metric}' in sample_metrics:
                bootstrap_metrics[metric].append(sample_metrics[f'conclusion_{metric}'])
    
    # Calculate confidence intervals
    alpha = 1 - confidence_level
    results = {}
    
    for metric in metric_names:
        if bootstrap_metrics[metric]:
            values = np.array(bootstrap_metrics[metric])
            results[metric] = {
                'mean': np.mean(values),
                'std': np.std(values),
                'ci_lower': np.percentile(values, 100 * alpha / 2),
                'ci_upper': np.percentile(values, 100 * (1 - alpha / 2))
            }
    
    return results