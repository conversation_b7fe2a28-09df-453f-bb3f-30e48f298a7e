import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Any, Optional
import logging

logger = logging.getLogger(__name__)


class FocalLoss(nn.Module):
    """Focal Loss for addressing class imbalance."""
    
    def __init__(self, alpha: float = 0.25, gamma: float = 2.0, reduction: str = 'mean'):
        """
        Initialize Focal Loss.
        
        Args:
            alpha: Weighting factor for rare class
            gamma: Focusing parameter
            reduction: Reduction method ('mean', 'sum', 'none')
        """
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Forward pass.
        
        Args:
            inputs: Predictions (logits) of shape (batch_size, 1)
            targets: Ground truth labels of shape (batch_size, 1)
            
        Returns:
            Focal loss value
        """
        # Apply sigmoid to get probabilities
        p = torch.sigmoid(inputs)
        
        # Flatten tensors
        p = p.view(-1)
        targets = targets.view(-1)
        
        # Calculate focal loss
        ce_loss = F.binary_cross_entropy(p, targets, reduction='none')
        p_t = p * targets + (1 - p) * (1 - targets)
        alpha_t = self.alpha * targets + (1 - self.alpha) * (1 - targets)
        focal_weight = alpha_t * (1 - p_t) ** self.gamma
        
        focal_loss = focal_weight * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class MultiTaskLoss(nn.Module):
    """Multi-task loss for MCG prediction combining regression and classification."""
    
    def __init__(self, risk_weight: float = 1.0, conclusion_weight: float = 1.0,
                 use_focal_loss: bool = True, focal_alpha: float = 0.25, 
                 focal_gamma: float = 2.0, risk_loss_type: str = 'bce'):
        """
        Initialize multi-task loss.
        
        Args:
            risk_weight: Weight for risk score loss
            conclusion_weight: Weight for conclusion loss
            use_focal_loss: Whether to use focal loss for conclusion
            focal_alpha: Alpha parameter for focal loss
            focal_gamma: Gamma parameter for focal loss
            risk_loss_type: Type of loss for risk scores ('bce', 'mse', 'smooth_l1')
        """
        super().__init__()
        
        self.risk_weight = risk_weight
        self.conclusion_weight = conclusion_weight
        self.use_focal_loss = use_focal_loss
        self.risk_loss_type = risk_loss_type
        
        # Risk score loss function
        if risk_loss_type == 'bce':
            self.risk_loss_fn = nn.BCELoss()
        elif risk_loss_type == 'mse':
            self.risk_loss_fn = nn.MSELoss()
        elif risk_loss_type == 'smooth_l1':
            self.risk_loss_fn = nn.SmoothL1Loss()
        else:
            raise ValueError(f"Unsupported risk loss type: {risk_loss_type}")
        
        # Conclusion loss function
        if use_focal_loss:
            self.conclusion_loss_fn = FocalLoss(alpha=focal_alpha, gamma=focal_gamma)
        else:
            self.conclusion_loss_fn = nn.BCEWithLogitsLoss()
        
        logger.info(f"MultiTaskLoss initialized: risk_weight={risk_weight}, "
                   f"conclusion_weight={conclusion_weight}, use_focal={use_focal_loss}")
    
    def forward(self, predictions: Dict[str, torch.Tensor], 
                targets: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Forward pass.
        
        Args:
            predictions: Dictionary containing model predictions
                - 'risk_scores': (batch_size, 4) sigmoid outputs
                - 'conclusion_logits': (batch_size, 1) raw logits
            targets: Dictionary containing ground truth labels
                - 'risk_scores': (batch_size, 4) target risk scores
                - 'conclusion': (batch_size, 1) target conclusion labels
                
        Returns:
            Dictionary containing loss components
        """
        # Risk score loss
        risk_pred = predictions['risk_scores']
        risk_target = targets['risk_scores']
        
        # Handle NaN values in targets
        valid_risk_mask = ~torch.isnan(risk_target)
        if valid_risk_mask.any():
            risk_loss = self.risk_loss_fn(
                risk_pred[valid_risk_mask], 
                risk_target[valid_risk_mask]
            )
        else:
            risk_loss = torch.tensor(0.0, device=risk_pred.device)
        
        # Conclusion loss
        conclusion_logits = predictions['conclusion_logits']
        conclusion_target = targets['conclusion']
        
        # Handle NaN values in targets
        valid_conclusion_mask = ~torch.isnan(conclusion_target)
        if valid_conclusion_mask.any():
            if self.use_focal_loss:
                conclusion_loss = self.conclusion_loss_fn(
                    conclusion_logits[valid_conclusion_mask],
                    conclusion_target[valid_conclusion_mask]
                )
            else:
                conclusion_loss = self.conclusion_loss_fn(
                    conclusion_logits[valid_conclusion_mask],
                    conclusion_target[valid_conclusion_mask]
                )
        else:
            conclusion_loss = torch.tensor(0.0, device=conclusion_logits.device)
        
        # Total loss
        total_loss = (self.risk_weight * risk_loss + 
                     self.conclusion_weight * conclusion_loss)
        
        return {
            'total_loss': total_loss,
            'risk_loss': risk_loss,
            'conclusion_loss': conclusion_loss,
            'risk_weight': self.risk_weight,
            'conclusion_weight': self.conclusion_weight
        }


class AdaptiveMultiTaskLoss(nn.Module):
    """Adaptive multi-task loss with learnable task weights."""
    
    def __init__(self, initial_risk_weight: float = 1.0, 
                 initial_conclusion_weight: float = 1.0,
                 use_focal_loss: bool = True, focal_alpha: float = 0.25,
                 focal_gamma: float = 2.0, risk_loss_type: str = 'bce'):
        """
        Initialize adaptive multi-task loss.
        
        Args:
            initial_risk_weight: Initial weight for risk score loss
            initial_conclusion_weight: Initial weight for conclusion loss
            use_focal_loss: Whether to use focal loss for conclusion
            focal_alpha: Alpha parameter for focal loss
            focal_gamma: Gamma parameter for focal loss
            risk_loss_type: Type of loss for risk scores
        """
        super().__init__()
        
        # Learnable task weights (log space for numerical stability)
        self.log_risk_weight = nn.Parameter(torch.log(torch.tensor(initial_risk_weight)))
        self.log_conclusion_weight = nn.Parameter(torch.log(torch.tensor(initial_conclusion_weight)))
        
        self.use_focal_loss = use_focal_loss
        self.risk_loss_type = risk_loss_type
        
        # Risk score loss function
        if risk_loss_type == 'bce':
            self.risk_loss_fn = nn.BCELoss()
        elif risk_loss_type == 'mse':
            self.risk_loss_fn = nn.MSELoss()
        elif risk_loss_type == 'smooth_l1':
            self.risk_loss_fn = nn.SmoothL1Loss()
        else:
            raise ValueError(f"Unsupported risk loss type: {risk_loss_type}")
        
        # Conclusion loss function
        if use_focal_loss:
            self.conclusion_loss_fn = FocalLoss(alpha=focal_alpha, gamma=focal_gamma)
        else:
            self.conclusion_loss_fn = nn.BCEWithLogitsLoss()
        
        logger.info("AdaptiveMultiTaskLoss initialized with learnable weights")
    
    def forward(self, predictions: Dict[str, torch.Tensor], 
                targets: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Forward pass with adaptive weights.
        
        Args:
            predictions: Dictionary containing model predictions
            targets: Dictionary containing ground truth labels
                
        Returns:
            Dictionary containing loss components
        """
        # Get current weights
        risk_weight = torch.exp(self.log_risk_weight)
        conclusion_weight = torch.exp(self.log_conclusion_weight)
        
        # Calculate individual losses
        risk_pred = predictions['risk_scores']
        risk_target = targets['risk_scores']
        
        valid_risk_mask = ~torch.isnan(risk_target)
        if valid_risk_mask.any():
            risk_loss = self.risk_loss_fn(
                risk_pred[valid_risk_mask], 
                risk_target[valid_risk_mask]
            )
        else:
            risk_loss = torch.tensor(0.0, device=risk_pred.device)
        
        conclusion_logits = predictions['conclusion_logits']
        conclusion_target = targets['conclusion']
        
        valid_conclusion_mask = ~torch.isnan(conclusion_target)
        if valid_conclusion_mask.any():
            conclusion_loss = self.conclusion_loss_fn(
                conclusion_logits[valid_conclusion_mask],
                conclusion_target[valid_conclusion_mask]
            )
        else:
            conclusion_loss = torch.tensor(0.0, device=conclusion_logits.device)
        
        # Adaptive weighting with uncertainty weighting
        # Following "Multi-Task Learning Using Uncertainty to Weigh Losses for Scene Geometry and Semantics"
        total_loss = (risk_loss / (2 * risk_weight**2) + 
                     conclusion_loss / (2 * conclusion_weight**2) +
                     torch.log(risk_weight) + torch.log(conclusion_weight))
        
        return {
            'total_loss': total_loss,
            'risk_loss': risk_loss,
            'conclusion_loss': conclusion_loss,
            'risk_weight': risk_weight.item(),
            'conclusion_weight': conclusion_weight.item()
        }


class ContrastiveLoss(nn.Module):
    """Contrastive loss for feature learning."""
    
    def __init__(self, temperature: float = 0.1, margin: float = 1.0):
        """
        Initialize contrastive loss.
        
        Args:
            temperature: Temperature parameter for contrastive learning
            margin: Margin for contrastive loss
        """
        super().__init__()
        self.temperature = temperature
        self.margin = margin
        
    def forward(self, features: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """
        Forward pass.
        
        Args:
            features: Feature representations of shape (batch_size, feature_dim)
            labels: Labels of shape (batch_size,)
            
        Returns:
            Contrastive loss value
        """
        batch_size = features.size(0)
        device = features.device
        
        # Normalize features
        features = F.normalize(features, dim=1)
        
        # Compute similarity matrix
        similarity_matrix = torch.matmul(features, features.T) / self.temperature
        
        # Create label mask
        labels = labels.view(-1, 1)
        mask = torch.eq(labels, labels.T).float().to(device)
        
        # Mask diagonal elements
        mask = mask - torch.eye(batch_size, device=device)
        
        # Compute positive and negative similarities
        positive_pairs = similarity_matrix * mask
        negative_pairs = similarity_matrix * (1 - mask)
        
        # Compute contrastive loss
        positive_loss = -torch.log(torch.exp(positive_pairs).sum(dim=1) + 1e-8)
        negative_loss = torch.log(torch.exp(negative_pairs).sum(dim=1) + 1e-8)
        
        contrastive_loss = (positive_loss + negative_loss).mean()
        
        return contrastive_loss


class CombinedLoss(nn.Module):
    """Combined loss function with multiple objectives."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize combined loss from configuration.
        
        Args:
            config: Loss configuration dictionary
        """
        super().__init__()
        
        loss_weights = config.get('loss_weights', {})
        
        # Multi-task loss
        self.multitask_loss = MultiTaskLoss(
            risk_weight=loss_weights.get('risk_score_weight', 1.0),
            conclusion_weight=loss_weights.get('conclusion_weight', 1.0),
            use_focal_loss=loss_weights.get('use_focal_loss', True),
            focal_alpha=loss_weights.get('focal_loss_alpha', 0.25),
            focal_gamma=loss_weights.get('focal_loss_gamma', 2.0),
            risk_loss_type=loss_weights.get('risk_loss_type', 'bce')
        )
        
        # Optional contrastive loss
        self.use_contrastive = loss_weights.get('use_contrastive_loss', False)
        self.contrastive_weight = loss_weights.get('contrastive_weight', 0.1)
        
        if self.use_contrastive:
            self.contrastive_loss = ContrastiveLoss(
                temperature=loss_weights.get('contrastive_temperature', 0.1),
                margin=loss_weights.get('contrastive_margin', 1.0)
            )
        
        logger.info(f"CombinedLoss initialized with contrastive={self.use_contrastive}")
    
    def forward(self, predictions: Dict[str, torch.Tensor], 
                targets: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Forward pass.
        
        Args:
            predictions: Dictionary containing model predictions
            targets: Dictionary containing ground truth labels
                
        Returns:
            Dictionary containing loss components
        """
        # Multi-task loss
        loss_dict = self.multitask_loss(predictions, targets)
        
        # Add contrastive loss if enabled
        if self.use_contrastive and 'fused_features' in predictions:
            # Use conclusion labels for contrastive learning
            conclusion_labels = targets['conclusion'].squeeze()
            contrastive_loss = self.contrastive_loss(
                predictions['fused_features'], conclusion_labels
            )
            loss_dict['contrastive_loss'] = contrastive_loss
            loss_dict['total_loss'] = loss_dict['total_loss'] + self.contrastive_weight * contrastive_loss
        
        return loss_dict


def create_loss_function(config: Dict[str, Any]) -> nn.Module:
    """
    Create loss function from configuration.
    
    Args:
        config: Training configuration dictionary
        
    Returns:
        Loss function module
    """
    loss_type = config.get('loss_weights', {}).get('loss_type', 'multitask')
    
    if loss_type == 'adaptive':
        loss_fn = AdaptiveMultiTaskLoss(
            initial_risk_weight=config['loss_weights'].get('risk_score_weight', 1.0),
            initial_conclusion_weight=config['loss_weights'].get('conclusion_weight', 1.0),
            use_focal_loss=config['loss_weights'].get('use_focal_loss', True),
            focal_alpha=config['loss_weights'].get('focal_loss_alpha', 0.25),
            focal_gamma=config['loss_weights'].get('focal_loss_gamma', 2.0),
            risk_loss_type=config['loss_weights'].get('risk_loss_type', 'bce')
        )
    elif loss_type == 'combined':
        loss_fn = CombinedLoss(config)
    else:
        loss_fn = MultiTaskLoss(
            risk_weight=config['loss_weights'].get('risk_score_weight', 1.0),
            conclusion_weight=config['loss_weights'].get('conclusion_weight', 1.0),
            use_focal_loss=config['loss_weights'].get('use_focal_loss', True),
            focal_alpha=config['loss_weights'].get('focal_loss_alpha', 0.25),
            focal_gamma=config['loss_weights'].get('focal_loss_gamma', 2.0),
            risk_loss_type=config['loss_weights'].get('risk_loss_type', 'bce')
        )
    
    return loss_fn