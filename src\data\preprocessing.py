import numpy as np
import torch
from typing import <PERSON><PERSON>, Optional, Dict, Any
from sklearn.preprocessing import StandardScaler, MaxAbsScaler, MinMaxScaler
import logging

logger = logging.getLogger(__name__)


class MCGPreprocessor:
    """Preprocessor for MCG spatiotemporal data."""
    
    def __init__(self, max_sequence_length: int = 2000, normalization: str = "standard", 
                 padding_strategy: str = "zeros", use_training_set_stats: bool = True):
        """
        Initialize MCG preprocessor.
        
        Args:
            max_sequence_length: Maximum sequence length for padding
            normalization: Normalization strategy ("standard", "maxabs", "minmax")
            padding_strategy: Padding strategy ("zeros", "reflect", "edge")
            use_training_set_stats: Whether to use training set statistics for normalization
        """
        self.max_sequence_length = max_sequence_length
        self.normalization = normalization
        self.padding_strategy = padding_strategy
        self.use_training_set_stats = use_training_set_stats
        self.fitted = False
        
        # Training set statistics
        self.training_mean = None
        self.training_std = None
        self.training_max_abs = None
        self.training_min = None
        self.training_max = None
    
    def pad_and_format_only(self, data: np.ndarray) -> np.ndarray:
        """
        对42通道数据只进行填充和格式转换，不进行归一化
        用于已经在multimodal_loader中归一化过的数据
        
        Args:
            data: 42通道数据 (42, n_timestamp)
            
        Returns:
            填充后的数据 (42, max_sequence_length)
        """
        # 检查输入格式
        if len(data.shape) != 2 or data.shape[0] != 42:
            raise ValueError(f"Expected 42-channel data with shape (42, n_timestamp), got {data.shape}")
        
        n_timestamp = data.shape[1]
        
        # 如果序列长度已经符合要求，直接返回
        if n_timestamp == self.max_sequence_length:
            return data
        
        # 填充或截断到目标长度
        if n_timestamp < self.max_sequence_length:
            # 填充
            pad_width = self.max_sequence_length - n_timestamp
            
            if self.padding_strategy == "zeros":
                padded_data = np.pad(data, ((0, 0), (0, pad_width)), mode='constant', constant_values=0)
            elif self.padding_strategy == "reflect":
                padded_data = np.pad(data, ((0, 0), (0, pad_width)), mode='reflect')
            elif self.padding_strategy == "edge":
                padded_data = np.pad(data, ((0, 0), (0, pad_width)), mode='edge')
            else:
                # 默认零填充
                padded_data = np.pad(data, ((0, 0), (0, pad_width)), mode='constant', constant_values=0)
                
        else:
            # 截断
            padded_data = data[:, :self.max_sequence_length]
        
        # 重要：这里不进行任何归一化，数据在multimodal_loader中已经归一化
        return padded_data
    
    def pad_sequence(self, data: np.ndarray) -> np.ndarray:
        """
        Pad sequence to fixed length.
        
        Args:
            data: Input data of shape (6, 6, n_timestamp) or (37, n_timestamp)
            
        Returns:
            Padded data of same spatial shape but with max_sequence_length
        """
        if len(data.shape) == 3:
            # Original format: (6, 6, n_timestamp)
            n_timestamp = data.shape[2]
            spatial_shape = (6, 6)
        elif len(data.shape) == 2:
            # New format with timepoint: (37, n_timestamp)
            n_timestamp = data.shape[1]
            spatial_shape = (37,)
        else:
            raise ValueError(f"Unsupported data shape: {data.shape}")
        
        if n_timestamp >= self.max_sequence_length:
            # Truncate if longer than max length
            if len(data.shape) == 3:
                return data[:, :, :self.max_sequence_length]
            else:
                return data[:, :self.max_sequence_length]
        
        # Calculate padding
        pad_length = self.max_sequence_length - n_timestamp
        
        if self.padding_strategy == "zeros":
            # Zero padding
            if len(data.shape) == 3:
                pad_array = np.zeros((6, 6, pad_length))
                padded_data = np.concatenate([data, pad_array], axis=2)
            else:
                pad_array = np.zeros((37, pad_length))
                padded_data = np.concatenate([data, pad_array], axis=1)
        elif self.padding_strategy == "reflect":
            # Reflect padding
            if len(data.shape) == 3:
                padded_data = np.pad(data, ((0, 0), (0, 0), (0, pad_length)), mode='reflect')
            else:
                padded_data = np.pad(data, ((0, 0), (0, pad_length)), mode='reflect')
        elif self.padding_strategy == "edge":
            # Edge padding (repeat last value)
            if len(data.shape) == 3:
                padded_data = np.pad(data, ((0, 0), (0, 0), (0, pad_length)), mode='edge')
            else:
                padded_data = np.pad(data, ((0, 0), (0, pad_length)), mode='edge')
        else:
            raise ValueError(f"Unsupported padding strategy: {self.padding_strategy}")
        
        return padded_data
    
    def normalize_data(self, data: np.ndarray, fit: bool = False) -> np.ndarray:
        """
        Normalize MCG data using either training set statistics or sample-wise normalization.
        
        Args:
            data: Input data of shape (batch_size, 6, 6, sequence_length), (6, 6, sequence_length), 
                  (batch_size, 37, sequence_length), or (37, sequence_length)
            fit: Whether to fit the normalization parameters on this data
            
        Returns:
            Normalized data
        """
        original_shape = data.shape
        
        # Handle different input formats
        if len(original_shape) == 2:
            # Single sample with 37 channels: (37, sequence_length)
            data = data.reshape(1, *original_shape)
            single_sample = True
            use_37_channels = True
        elif len(original_shape) == 3:
            if original_shape[0] == 37:
                # Batch with 37 channels: (batch_size, 37, sequence_length)
                single_sample = False
                use_37_channels = True
            else:
                # Single sample with 6x6: (6, 6, sequence_length)
                data = data.reshape(1, *original_shape)
                single_sample = True
                use_37_channels = False
        elif len(original_shape) == 4:
            # Batch with 6x6: (batch_size, 6, 6, sequence_length)
            single_sample = False
            use_37_channels = False
        else:
            raise ValueError(f"Unsupported data shape: {original_shape}")
        
        if use_37_channels:
            batch_size, n_channels, seq_len = data.shape
        else:
            batch_size, height, width, seq_len = data.shape
        
        if self.use_training_set_stats:
            # Use training set statistics for normalization
            if fit:
                # Fit normalization parameters on training data
                self._fit_training_statistics(data)
                self.fitted = True
                logger.info(f"Fitted normalization statistics on {batch_size} training samples")
            
            if not self.fitted:
                raise ValueError("Normalization not fitted. Call with fit=True on training data first.")
            
            # Apply training set normalization
            normalized_data = self._apply_training_normalization(data)
        else:
            # Sample-wise normalization: normalize each sample independently
            normalized_data = self._apply_sample_wise_normalization(data)
            self.fitted = True
        
        if single_sample:
            normalized_data = normalized_data[0]
        
        return normalized_data
    
    def _fit_training_statistics(self, data: np.ndarray) -> None:
        """
        Fit normalization statistics on training data.
        
        Args:
            data: Training data of shape (batch_size, 6, 6, sequence_length)
        """
        if self.normalization == 'standard':
            self.training_mean = np.mean(data)
            self.training_std = np.std(data)
            logger.info(f"Training set statistics - Mean: {self.training_mean:.6f}, Std: {self.training_std:.6f}")
            
        elif self.normalization == 'maxabs':
            self.training_max_abs = np.max(np.abs(data))
            logger.info(f"Training set statistics - Max abs: {self.training_max_abs:.6f}")
            
        elif self.normalization == 'minmax':
            self.training_min = np.min(data)
            self.training_max = np.max(data)
            logger.info(f"Training set statistics - Min: {self.training_min:.6f}, Max: {self.training_max:.6f}")
    
    def _apply_training_normalization(self, data: np.ndarray) -> np.ndarray:
        """
        Apply training set normalization to data.
        
        Args:
            data: Input data
            
        Returns:
            Normalized data
        """
        if self.normalization == 'standard':
            if self.training_std > 1e-8:
                return (data - self.training_mean) / self.training_std
            else:
                return data
                
        elif self.normalization == 'maxabs':
            if self.training_max_abs > 1e-8:
                return data / self.training_max_abs
            else:
                return data
                
        elif self.normalization == 'minmax':
            if (self.training_max - self.training_min) > 1e-8:
                return (data - self.training_min) / (self.training_max - self.training_min)
            else:
                return data
        else:
            return data
    
    def _apply_sample_wise_normalization(self, data: np.ndarray) -> np.ndarray:
        """
        Apply sample-wise normalization to data.
        
        Args:
            data: Input data of shape (batch_size, 6, 6, sequence_length) or (batch_size, 37, sequence_length)
            
        Returns:
            Normalized data
        """
        # Handle different data formats
        if len(data.shape) == 3:
            # 37-channel format: (batch_size, 37, seq_len)
            batch_size, n_channels, seq_len = data.shape
            use_37_channels = True
        else:
            # 6x6 format: (batch_size, 6, 6, seq_len)
            batch_size, height, width, seq_len = data.shape
            use_37_channels = False
        
        normalized_data = np.zeros_like(data)
        
        for i in range(batch_size):
            sample_data = data[i]  # (37, seq_len) or (6, 6, seq_len)
            
            if self.normalization == 'standard':
                # Z-score normalization per sample
                mean = np.mean(sample_data)
                std = np.std(sample_data)
                if std > 1e-8:  # Avoid division by zero
                    normalized_data[i] = (sample_data - mean) / std
                else:
                    normalized_data[i] = sample_data
                    
            elif self.normalization == 'maxabs':
                # Max absolute value normalization per sample
                max_abs = np.max(np.abs(sample_data))
                if max_abs > 1e-8:
                    normalized_data[i] = sample_data / max_abs
                else:
                    normalized_data[i] = sample_data
                    
            elif self.normalization == 'minmax':
                # Min-max normalization per sample
                min_val = np.min(sample_data)
                max_val = np.max(sample_data)
                if (max_val - min_val) > 1e-8:
                    normalized_data[i] = (sample_data - min_val) / (max_val - min_val)
                else:
                    normalized_data[i] = sample_data
            else:
                # No normalization
                normalized_data[i] = sample_data
        
        return normalized_data
    
    def preprocess_single(self, data: np.ndarray, fit: bool = False) -> np.ndarray:
        """
        Preprocess a single MCG sample.
        
        Args:
            data: Input data of shape (6, 6, n_timestamp) or (37, n_timestamp)
            fit: Whether to fit normalization parameters
            
        Returns:
            Preprocessed data of same spatial shape but with max_sequence_length
        """
        # Pad sequence
        padded_data = self.pad_sequence(data)
        
        # Normalize (always apply normalization)
        normalized_data = self.normalize_data(padded_data, fit=fit)
        
        return normalized_data
    
    def preprocess_batch(self, data_list: list, fit: bool = False) -> np.ndarray:
        """
        Preprocess a batch of MCG samples.
        
        Args:
            data_list: List of data arrays, each of shape (6, 6, n_timestamp)
            fit: Whether to fit normalization parameters
            
        Returns:
            Preprocessed batch of shape (batch_size, 6, 6, max_sequence_length)
        """
        processed_samples = []
        
        for i, data in enumerate(data_list):
            # For fitting, use all samples; for transform, don't fit
            sample_fit = fit if i == 0 else False
            processed_data = self.preprocess_single(data, fit=sample_fit)
            processed_samples.append(processed_data)
        
        # Stack into batch
        batch_data = np.stack(processed_samples, axis=0)
        
        # For sample-wise normalization, no need for batch fitting
        # Each sample is normalized independently
        
        return batch_data
    
    def to_pytorch_format(self, data: np.ndarray) -> torch.Tensor:
        """
        Convert preprocessed data to PyTorch format.
        
        Args:
            data: Preprocessed data of shape (42, sequence_length), (37, sequence_length), or (6, 6, sequence_length)
            
        Returns:
            Tensor in format suitable for the model: (sequence_length, num_channels) for multimodal data
        """
        # Handle different input formats
        if len(data.shape) == 2:
            # Check format based on first dimension
            if data.shape[0] == 42:
                # 42-channel multimodal format: (42, sequence_length) -> (sequence_length, 42)
                tensor_data = torch.FloatTensor(data.T)  # Transpose to (sequence_length, 42)
                
            elif data.shape[0] == 37:
                # 37-channel format: (37, sequence_length) -> (sequence_length, 37)
                tensor_data = torch.FloatTensor(data.T)  # Transpose to (sequence_length, 37)
                
            elif data.shape[0] == 36:
                # 36-channel format: (36, sequence_length) -> (sequence_length, 36)
                tensor_data = torch.FloatTensor(data.T)  # Transpose to (sequence_length, 36)
                
            else:
                raise ValueError(f"Unsupported 2D data shape: {data.shape}")
                
        elif len(data.shape) == 3:
            if data.shape[0] == 6 and data.shape[1] == 6:
                # Traditional 6x6 spatial format: (6, 6, sequence_length) -> (sequence_length, 1, 6, 6)
                sequence_length = data.shape[2]
                # Permute to (sequence_length, 6, 6) then add channel dimension
                tensor_data = torch.FloatTensor(data).permute(2, 0, 1).unsqueeze(1)
            else:
                raise ValueError(f"Unsupported 3D data shape: {data.shape}")
        else:
            raise ValueError(f"Unsupported data shape: {data.shape}")
        
        return tensor_data


class DataAugmentor:
    """Data augmentation for MCG data."""
    
    def __init__(self, noise_std: float = 0.1, mixup_alpha: float = 0.2):
        """
        Initialize data augmentor.
        
        Args:
            noise_std: Standard deviation for Gaussian noise
            mixup_alpha: Alpha parameter for mixup augmentation
        """
        self.noise_std = noise_std
        self.mixup_alpha = mixup_alpha
    
    def add_noise(self, data: np.ndarray, noise_type: str = "gaussian") -> np.ndarray:
        """
        Add noise to MCG data.
        
        Args:
            data: Input data
            noise_type: Type of noise ("gaussian", "sinusoidal")
            
        Returns:
            Noisy data
        """
        if noise_type == "gaussian":
            noise = np.random.normal(0, self.noise_std, data.shape)
            return data + noise
        elif noise_type == "sinusoidal":
            # Add unstable high-frequency sinusoidal noise (20-50Hz)
            seq_len = data.shape[-1]
            time_steps = np.linspace(0, 1, seq_len)
            
            # Random frequency between 20-50Hz
            freq = np.random.uniform(20, 50)
            # Random phase
            phase = np.random.uniform(0, 2 * np.pi)
            # Random amplitude
            amplitude = np.random.uniform(0.01, 0.05) * np.std(data)
            
            noise = amplitude * np.sin(2 * np.pi * freq * time_steps + phase)
            
            # Broadcast noise to all spatial locations
            noise_full = np.broadcast_to(noise, data.shape)
            
            return data + noise_full
        else:
            raise ValueError(f"Unsupported noise type: {noise_type}")
    
    def mixup(self, data1: np.ndarray, data2: np.ndarray, 
              labels1: Dict[str, np.ndarray], labels2: Dict[str, np.ndarray]) -> Tuple[np.ndarray, Dict[str, np.ndarray]]:
        """
        Apply mixup augmentation.
        
        Args:
            data1, data2: Input data samples
            labels1, labels2: Corresponding labels
            
        Returns:
            Mixed data and labels
        """
        # Sample lambda from Beta distribution
        lam = np.random.beta(self.mixup_alpha, self.mixup_alpha)
        
        # Mix data
        mixed_data = lam * data1 + (1 - lam) * data2
        
        # Mix labels
        mixed_labels = {}
        for key in labels1.keys():
            mixed_labels[key] = lam * labels1[key] + (1 - lam) * labels2[key]
        
        return mixed_data, mixed_labels
    
    def random_crop(self, data: np.ndarray, crop_ratio: float = 0.8) -> np.ndarray:
        """
        Random temporal cropping.
        
        Args:
            data: Input data of shape (..., sequence_length)
            crop_ratio: Ratio of sequence to keep
            
        Returns:
            Cropped and padded data
        """
        seq_len = data.shape[-1]
        crop_len = int(seq_len * crop_ratio)
        
        # Random start position
        start_pos = np.random.randint(0, seq_len - crop_len + 1)
        
        # Crop
        cropped_data = data[..., start_pos:start_pos + crop_len]
        
        # Pad back to original length
        pad_len = seq_len - crop_len
        if pad_len > 0:
            # Pad with zeros
            pad_shape = list(cropped_data.shape)
            pad_shape[-1] = pad_len
            padding = np.zeros(pad_shape)
            cropped_data = np.concatenate([cropped_data, padding], axis=-1)
        
        return cropped_data


def create_preprocessing_pipeline(config: Dict[str, Any]) -> MCGPreprocessor:
    """
    Create preprocessing pipeline from configuration.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Configured MCGPreprocessor
    """
    preprocessor = MCGPreprocessor(
        max_sequence_length=getattr(config, 'max_sequence_length', 2000),
        normalization=getattr(config.preprocessing, 'normalization', 'standard') if hasattr(config, 'preprocessing') else 'standard',
        padding_strategy=getattr(config.preprocessing, 'padding_strategy', 'zeros') if hasattr(config, 'preprocessing') else 'zeros',
        use_training_set_stats=getattr(config.preprocessing, 'use_training_set_stats', True) if hasattr(config, 'preprocessing') else True
    )
    
    return preprocessor


def create_augmentor(config: Dict[str, Any]) -> Optional[DataAugmentor]:
    """
    Create data augmentor from configuration.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        DataAugmentor if enabled, None otherwise
    """
    if hasattr(config, 'augmentation') and config.augmentation:
        aug_config = config.augmentation
        if not getattr(aug_config, 'enabled', False):
            return None
        
        augmentor = DataAugmentor(
            noise_std=getattr(aug_config, 'noise_std', 0.1),
            mixup_alpha=getattr(aug_config, 'mixup_alpha', 0.2)
        )
        return augmentor
    else:
        return None