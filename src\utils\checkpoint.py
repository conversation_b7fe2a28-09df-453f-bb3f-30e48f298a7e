import os
import torch
import torch.nn as nn
from typing import Dict, Any, Optional, Tuple
import logging
import json
from datetime import datetime
import shutil

logger = logging.getLogger(__name__)


class CheckpointManager:
    """Manage model checkpoints and persistence."""
    
    def __init__(self, checkpoint_dir: str, max_checkpoints: int = 5):
        """
        Initialize checkpoint manager.
        
        Args:
            checkpoint_dir: Directory to save checkpoints
            max_checkpoints: Maximum number of checkpoints to keep
        """
        self.checkpoint_dir = checkpoint_dir
        self.max_checkpoints = max_checkpoints
        
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        self.checkpoint_history = []  # List of (epoch, score, filepath) tuples
        
    def save_checkpoint(self, model: nn.Module, optimizer: torch.optim.Optimizer,
                       epoch: int, metrics: Dict[str, float], config: Any,
                       scheduler: Optional[Any] = None, is_best: bool = False,
                       model_name: str = "model") -> str:
        """
        Save model checkpoint.
        
        Args:
            model: PyTorch model
            optimizer: Optimizer state
            epoch: Current epoch
            metrics: Dictionary of metrics
            config: Configuration object
            scheduler: Learning rate scheduler (optional)
            is_best: Whether this is the best model so far
            model_name: Name prefix for the checkpoint file
            
        Returns:
            Path to saved checkpoint
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if is_best:
            filename = f"{model_name}_best.pth"
        else:
            filename = f"{model_name}_epoch_{epoch+1}_{timestamp}.pth"
        
        filepath = os.path.join(self.checkpoint_dir, filename)
        
        # Prepare checkpoint data
        checkpoint_data = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'metrics': metrics,
            'config': config.__dict__ if hasattr(config, '__dict__') else config,
            'timestamp': timestamp,
            'is_best': is_best
        }
        
        # Add scheduler state if available
        if scheduler is not None:
            if hasattr(scheduler, 'scheduler') and scheduler.scheduler is not None:
                checkpoint_data['scheduler_state_dict'] = scheduler.scheduler.state_dict()
            elif hasattr(scheduler, 'state_dict'):
                checkpoint_data['scheduler_state_dict'] = scheduler.state_dict()
        
        # Save checkpoint
        torch.save(checkpoint_data, filepath)
        
        # Update checkpoint history
        val_loss = metrics.get('total_loss', metrics.get('val_loss', float('inf')))
        self.checkpoint_history.append((epoch, val_loss, filepath))
        
        # Clean up old checkpoints (keep best and recent ones)
        if not is_best:
            self._cleanup_checkpoints()
        
        logger.info(f"Checkpoint saved: {filepath}")
        return filepath
    
    def load_checkpoint(self, filepath: str, model: nn.Module, 
                       optimizer: Optional[torch.optim.Optimizer] = None,
                       scheduler: Optional[Any] = None,
                       device: Optional[torch.device] = None) -> Dict[str, Any]:
        """
        Load model checkpoint.
        
        Args:
            filepath: Path to checkpoint file
            model: PyTorch model to load state into
            optimizer: Optimizer to load state into (optional)
            scheduler: Scheduler to load state into (optional)
            device: Device to load checkpoint on
            
        Returns:
            Dictionary with checkpoint information
        """
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Checkpoint not found: {filepath}")
        
        logger.info(f"Loading checkpoint: {filepath}")
        
        if device is None:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        checkpoint = torch.load(filepath, map_location=device)
        
        # Load model state
        model.load_state_dict(checkpoint['model_state_dict'])
        
        # Load optimizer state if provided
        if optimizer is not None and 'optimizer_state_dict' in checkpoint:
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        # Load scheduler state if provided
        if scheduler is not None and 'scheduler_state_dict' in checkpoint:
            if hasattr(scheduler, 'scheduler') and scheduler.scheduler is not None:
                scheduler.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            elif hasattr(scheduler, 'load_state_dict'):
                scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        logger.info(f"Checkpoint loaded from epoch {checkpoint['epoch']}")
        
        return {
            'epoch': checkpoint['epoch'],
            'metrics': checkpoint.get('metrics', {}),
            'config': checkpoint.get('config', {}),
            'timestamp': checkpoint.get('timestamp', 'unknown')
        }
    
    def load_best_checkpoint(self, model: nn.Module, model_name: str = "model",
                            optimizer: Optional[torch.optim.Optimizer] = None,
                            scheduler: Optional[Any] = None,
                            device: Optional[torch.device] = None) -> Optional[Dict[str, Any]]:
        """
        Load the best checkpoint.
        
        Args:
            model: PyTorch model
            model_name: Model name prefix
            optimizer: Optimizer (optional)
            scheduler: Scheduler (optional)
            device: Device to load on
            
        Returns:
            Checkpoint information or None if not found
        """
        best_checkpoint_path = os.path.join(self.checkpoint_dir, f"{model_name}_best.pth")
        
        if os.path.exists(best_checkpoint_path):
            return self.load_checkpoint(best_checkpoint_path, model, optimizer, scheduler, device)
        else:
            logger.warning(f"Best checkpoint not found: {best_checkpoint_path}")
            return None
    
    def _cleanup_checkpoints(self):
        """Clean up old checkpoints to maintain max_checkpoints limit."""
        if len(self.checkpoint_history) <= self.max_checkpoints:
            return
        
        # Sort by validation loss (lower is better)
        self.checkpoint_history.sort(key=lambda x: x[1])
        
        # Keep the best checkpoints and remove the rest
        to_remove = self.checkpoint_history[self.max_checkpoints:]
        self.checkpoint_history = self.checkpoint_history[:self.max_checkpoints]
        
        for epoch, score, filepath in to_remove:
            if os.path.exists(filepath) and not filepath.endswith('_best.pth'):
                try:
                    os.remove(filepath)
                    logger.info(f"Removed old checkpoint: {filepath}")
                except OSError as e:
                    logger.warning(f"Could not remove checkpoint {filepath}: {e}")
    
    def list_checkpoints(self) -> list:
        """
        List all available checkpoints.
        
        Returns:
            List of checkpoint information dictionaries
        """
        checkpoints = []
        
        for filename in os.listdir(self.checkpoint_dir):
            if filename.endswith('.pth'):
                filepath = os.path.join(self.checkpoint_dir, filename)
                try:
                    checkpoint = torch.load(filepath, map_location='cpu')
                    checkpoints.append({
                        'filename': filename,
                        'filepath': filepath,
                        'epoch': checkpoint.get('epoch', -1),
                        'metrics': checkpoint.get('metrics', {}),
                        'timestamp': checkpoint.get('timestamp', 'unknown'),
                        'is_best': checkpoint.get('is_best', False)
                    })
                except Exception as e:
                    logger.warning(f"Could not load checkpoint info from {filepath}: {e}")
        
        # Sort by epoch
        checkpoints.sort(key=lambda x: x['epoch'])
        return checkpoints
    
    def export_model(self, model: nn.Module, export_path: str, 
                    format: str = 'pytorch', include_config: bool = True,
                    config: Optional[Any] = None) -> str:
        """
        Export model for deployment.
        
        Args:
            model: Trained PyTorch model
            export_path: Path to save exported model
            format: Export format ('pytorch', 'onnx', 'torchscript')
            include_config: Whether to include configuration
            config: Configuration object
            
        Returns:
            Path to exported model
        """
        os.makedirs(os.path.dirname(export_path), exist_ok=True)
        
        if format == 'pytorch':
            # Save as standard PyTorch model
            export_data = {
                'model_state_dict': model.state_dict(),
                'model_class': model.__class__.__name__,
                'export_timestamp': datetime.now().isoformat()
            }
            
            if include_config and config is not None:
                export_data['config'] = config.__dict__ if hasattr(config, '__dict__') else config
            
            import torch as torch_module
            torch_module.save(export_data, export_path)
            
        elif format == 'torchscript':
            # Export as TorchScript
            model.eval()
            scripted_model = torch.jit.script(model)
            scripted_model.save(export_path)
            
        elif format == 'onnx':
            # Export as ONNX (requires sample input)
            try:
                import torch.onnx
                model.eval()
                
                # Create dummy input (you may need to adjust dimensions)
                dummy_mcg = torch.randn(1, 2000, 1, 6, 6)
                dummy_clinical = torch.randn(1, 10)  # Adjust based on your clinical features
                
                torch.onnx.export(
                    model,
                    (dummy_mcg, dummy_clinical),
                    export_path,
                    export_params=True,
                    opset_version=11,
                    do_constant_folding=True,
                    input_names=['mcg_data', 'clinical_features'],
                    output_names=['risk_scores', 'conclusion_logits']
                )
            except ImportError:
                raise ImportError("ONNX export requires onnx package: pip install onnx")
        else:
            raise ValueError(f"Unsupported export format: {format}")
        
        logger.info(f"Model exported to {export_path} in {format} format")
        return export_path


class ModelSaver:
    """Utility class for saving and loading models with metadata."""
    
    @staticmethod
    def save_model_with_metadata(model: nn.Module, filepath: str, 
                                metadata: Dict[str, Any]) -> None:
        """
        Save model with additional metadata.
        
        Args:
            model: PyTorch model
            filepath: Path to save model
            metadata: Additional metadata dictionary
        """
        save_data = {
            'model_state_dict': model.state_dict(),
            'model_class': model.__class__.__name__,
            'metadata': metadata,
            'save_timestamp': datetime.now().isoformat()
        }
        
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        torch.save(save_data, filepath)
        logger.info(f"Model with metadata saved to {filepath}")
    
    @staticmethod
    def load_model_with_metadata(filepath: str, model: nn.Module,
                                device: Optional[torch.device] = None) -> Dict[str, Any]:
        """
        Load model with metadata.
        
        Args:
            filepath: Path to model file
            model: Model instance to load state into
            device: Device to load on
            
        Returns:
            Metadata dictionary
        """
        if device is None:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        data = torch.load(filepath, map_location=device)
        model.load_state_dict(data['model_state_dict'])
        
        logger.info(f"Model loaded from {filepath}")
        return data.get('metadata', {})
    
    @staticmethod
    def create_model_archive(model: nn.Module, config: Any, metrics: Dict[str, float],
                           archive_path: str, include_code: bool = False) -> str:
        """
        Create a complete model archive with all necessary files.
        
        Args:
            model: Trained model
            config: Configuration
            metrics: Training metrics
            archive_path: Path for the archive
            include_code: Whether to include source code
            
        Returns:
            Path to created archive
        """
        import tempfile
        import zipfile
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Save model
            model_path = os.path.join(temp_dir, 'model.pth')
            torch.save({
                'model_state_dict': model.state_dict(),
                'model_class': model.__class__.__name__
            }, model_path)
            
            # Save configuration
            config_path = os.path.join(temp_dir, 'config.json')
            config_dict = config.__dict__ if hasattr(config, '__dict__') else config
            with open(config_path, 'w') as f:
                json.dump(config_dict, f, indent=2, default=str)
            
            # Save metrics
            metrics_path = os.path.join(temp_dir, 'metrics.json')
            with open(metrics_path, 'w') as f:
                json.dump(metrics, f, indent=2, default=str)
            
            # Save metadata
            metadata_path = os.path.join(temp_dir, 'metadata.json')
            metadata = {
                'creation_date': datetime.now().isoformat(),
                'model_class': model.__class__.__name__,
                'total_parameters': sum(p.numel() for p in model.parameters()),
                'trainable_parameters': sum(p.numel() for p in model.parameters() if p.requires_grad)
            }
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2, default=str)
            
            # Create archive
            os.makedirs(os.path.dirname(archive_path), exist_ok=True)
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(model_path, 'model.pth')
                zipf.write(config_path, 'config.json')
                zipf.write(metrics_path, 'metrics.json')
                zipf.write(metadata_path, 'metadata.json')
                
                # Include source code if requested
                if include_code:
                    src_dir = os.path.join(os.path.dirname(__file__), '..', '..')
                    for root, dirs, files in os.walk(src_dir):
                        for file in files:
                            if file.endswith('.py'):
                                file_path = os.path.join(root, file)
                                arc_path = os.path.relpath(file_path, src_dir)
                                zipf.write(file_path, f'src/{arc_path}')
        
        logger.info(f"Model archive created: {archive_path}")
        return archive_path


def auto_save_callback(checkpoint_manager: CheckpointManager, 
                      save_frequency: int = 5, save_best: bool = True):
    """
    Create a callback function for automatic model saving during training.
    
    Args:
        checkpoint_manager: CheckpointManager instance
        save_frequency: Save every N epochs
        save_best: Whether to save best model
        
    Returns:
        Callback function
    """
    best_score = float('inf')
    
    def callback(epoch: int, model: nn.Module, optimizer: torch.optim.Optimizer,
                metrics: Dict[str, float], config: Any, scheduler: Optional[Any] = None):
        nonlocal best_score
        
        current_score = metrics.get('total_loss', metrics.get('val_loss', float('inf')))
        
        # Save at specified frequency
        if (epoch + 1) % save_frequency == 0:
            checkpoint_manager.save_checkpoint(
                model, optimizer, epoch, metrics, config, scheduler
            )
        
        # Save if best
        if save_best and current_score < best_score:
            best_score = current_score
            checkpoint_manager.save_checkpoint(
                model, optimizer, epoch, metrics, config, scheduler, is_best=True
            )
    
    return callback