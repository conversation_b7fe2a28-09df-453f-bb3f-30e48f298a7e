"""
简化的风险分类损失函数
Simplified Risk Classification Loss Function
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class FocalLoss(nn.Module):
    """Focal Loss for addressing class imbalance."""
    
    def __init__(self, alpha: float = 0.25, gamma: float = 2.0, reduction: str = 'mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        # Apply sigmoid to get probabilities
        p = torch.sigmoid(inputs)
        
        # Flatten tensors
        p = p.view(-1)
        targets = targets.view(-1)
        
        # Calculate focal loss
        ce_loss = F.binary_cross_entropy(p, targets, reduction='none')
        p_t = p * targets + (1 - p) * (1 - targets)
        alpha_t = self.alpha * targets + (1 - self.alpha) * (1 - targets)
        focal_weight = alpha_t * (1 - p_t) ** self.gamma
        
        focal_loss = focal_weight * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


def convert_stenosis_to_risk_labels(stenosis_values: torch.Tensor, 
                                   risk_type: str = "regression", 
                                   threshold: float = 50.0) -> torch.Tensor:
    """
    将原始狭窄程度转换为风险标签
    
    Args:
        stenosis_values: 原始狭窄程度 (0-100 或 0-1)
        risk_type: "regression" 或 "classification"
        threshold: 分类阈值 (0-100范围内的百分比)
    
    Returns:
        风险标签
    """
    # 确保值在0-1范围内
    if stenosis_values.max() > 1.0:
        normalized_values = stenosis_values / 100.0
    else:
        normalized_values = stenosis_values
    
    if risk_type == "regression":
        # sigmoid(10 * stenosis - 5) 变换
        return torch.sigmoid(10 * normalized_values - 5)
    else:  # classification
        # 二分类: >threshold 为 1, ≤threshold 为 0
        threshold_normalized = threshold / 100.0
        return (normalized_values > threshold_normalized).float()


class SimpleMultiTaskLoss(nn.Module):
    """简化的多任务损失函数，支持风险分类/回归配置"""
    
    def __init__(self, risk_score_weight: float = 1.0, conclusion_weight: float = 1.0,
                 focal_loss_alpha: float = 0.25, focal_loss_gamma: float = 2.0,
                 risk_type: str = "regression", risk_threshold: float = 50.0):
        """
        初始化简化多任务损失函数
        
        Args:
            risk_score_weight: 风险评分损失权重
            conclusion_weight: 结论损失权重
            focal_loss_alpha: Focal Loss的alpha参数
            focal_loss_gamma: Focal Loss的gamma参数
            risk_type: 风险类型 "regression" 或 "classification"
            risk_threshold: 风险分类阈值 (百分比, 0-100)
        """
        super().__init__()
        
        self.risk_score_weight = risk_score_weight
        self.conclusion_weight = conclusion_weight
        self.risk_type = risk_type
        self.risk_threshold = risk_threshold
        
        # 风险损失函数
        if risk_type == "classification":
            self.risk_loss_fn = FocalLoss(alpha=focal_loss_alpha, gamma=focal_loss_gamma)
        else:  # regression
            self.risk_loss_fn = nn.BCELoss()
        
        # 结论损失函数 (固定为分类)
        self.conclusion_loss_fn = FocalLoss(alpha=focal_loss_alpha, gamma=focal_loss_gamma)
        
        logger.info(f"SimpleMultiTaskLoss initialized: risk_type={risk_type}, "
                   f"risk_threshold={risk_threshold}%, "
                   f"weights=({risk_score_weight}:{conclusion_weight})")
    
    def forward(self, predictions: Dict[str, torch.Tensor], 
                targets: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            predictions: 模型预测
                - 'risk_scores': (batch_size, 4) 风险评分/logits
                - 'conclusion_logits': (batch_size, 1) 结论logits
            targets: 真实标签
                - 'risk_scores': (batch_size, 4) 风险目标
                - 'conclusion': (batch_size, 1) 结论目标
                - 'original_stenosis': (batch_size, 4) 原始狭窄程度 [可选]
                
        Returns:
            损失组件字典
        """
        device = predictions['risk_scores'].device
        
        # 1. 风险损失计算
        risk_pred = predictions['risk_scores']
        risk_targets = targets['risk_scores']
        
        # 如果提供了原始狭窄程度，使用它来生成标签
        if 'original_stenosis' in targets:
            original_stenosis = targets['original_stenosis']
            risk_targets = convert_stenosis_to_risk_labels(
                original_stenosis, self.risk_type, self.risk_threshold
            )
        
        # 处理NaN值
        valid_risk_mask = ~torch.isnan(risk_targets)
        if valid_risk_mask.any():
            if self.risk_type == "classification":
                # 分类模式：risk_pred应该是logits
                risk_loss = self.risk_loss_fn(
                    risk_pred[valid_risk_mask], 
                    risk_targets[valid_risk_mask]
                )
            else:
                # 回归模式：risk_pred应该是sigmoid输出
                risk_loss = self.risk_loss_fn(
                    risk_pred[valid_risk_mask], 
                    risk_targets[valid_risk_mask]
                )
        else:
            risk_loss = torch.tensor(0.0, device=device)
        
        # 2. 结论损失计算
        conclusion_logits = predictions['conclusion_logits']
        conclusion_targets = targets['conclusion']
        
        valid_conclusion_mask = ~torch.isnan(conclusion_targets)
        if valid_conclusion_mask.any():
            conclusion_loss = self.conclusion_loss_fn(
                conclusion_logits[valid_conclusion_mask],
                conclusion_targets[valid_conclusion_mask]
            )
        else:
            conclusion_loss = torch.tensor(0.0, device=device)
        
        # 3. 总损失
        total_loss = (self.risk_score_weight * risk_loss + 
                     self.conclusion_weight * conclusion_loss)
        
        return {
            'total_loss': total_loss,
            'risk_loss': risk_loss,
            'conclusion_loss': conclusion_loss,
            'risk_score_weight': self.risk_score_weight,
            'conclusion_weight': self.conclusion_weight
        }


def create_loss_function(config: Dict[str, Any]) -> nn.Module:
    """
    根据配置创建损失函数
    
    Args:
        config: 训练配置字典
        
    Returns:
        损失函数模块
    """
    loss_weights = config.get('loss_weights', {})
    
    # 从配置中提取参数
    risk_score_weight = loss_weights.get('risk_score_weight', 1.0)
    conclusion_weight = loss_weights.get('conclusion_weight', 1.0)
    focal_loss_alpha = loss_weights.get('focal_loss_alpha', 0.25)
    focal_loss_gamma = loss_weights.get('focal_loss_gamma', 2.0)
    
    # 新增的风险类型配置
    risk_type = loss_weights.get('risk_type', 'regression')
    risk_threshold = loss_weights.get('risk_threshold', 50.0)
    
    return SimpleMultiTaskLoss(
        risk_score_weight=risk_score_weight,
        conclusion_weight=conclusion_weight,
        focal_loss_alpha=focal_loss_alpha,
        focal_loss_gamma=focal_loss_gamma,
        risk_type=risk_type,
        risk_threshold=risk_threshold
    )